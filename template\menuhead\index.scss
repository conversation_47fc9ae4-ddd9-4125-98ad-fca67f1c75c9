.position {
  position: absolute;
}

.nav {
  position: relative;
  display: flex;
  /* border-top: 1px solid #f7f7f7; */
  background: #fff;
  height: auto;
}

/* 高度 40+56 +1 rpx */
.nav-child {
  display: flex;
  flex: 1;
  text-align: center;
  height: 88rpx;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.borders-right {
  border-right: 1px solid #e6e6e6;
}

.borders-left {
  border-left: 1px solid #e6e6e6;
}

.borders {
  border-left: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
}

.nav-title {
  display: inline-block;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav .nav-child .icona {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20rpx;
  height: 20rpx;
  margin-left: 12rpx;
}

.icona image {
  width: 100%;
  height: 100%;
}

.active .iconb {
  margin-top: 0rpx !important;
}

/* 使用

@import "../../template/menuhead/index.scss"; */
