.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

@-webkit-keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.weui-mask {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

/* 内容 */
.cont {
  position: relative;
  width: 702rpx;
  height: 876rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  /* margin-bottom: 10rpx; */
}

.vVip {
  position: absolute;
  width: 310rpx;
  height: 174rpx;
  top: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.vClose {
  position: absolute;
  width: 28rpx;
  height: 28rpx;
  right: 26rpx;
  top: 26rpx;
  z-index: 1;
}

.head {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 222rpx;
  font-family:
    PingFang SC-Semibold,
    PingFang SC;
}

.head .one {
  position: relative;
  z-index: 1;
  padding-top: 94rpx;
  font-size: 40rpx;
  font-weight: 600;
  color: #573709;
  line-height: 47rpx;
}

.head .two {
  position: relative;
  z-index: 1;
  font-size: 28rpx;
  font-weight: 400;
  color: #573709;
  line-height: 33rpx;
}

.head .vbg {
  position: absolute;
  height: 222rpx;
  left: 0;
  top: 0;
}

/* 滚动部分 */
.cont .scroll {
  height: 654rpx;
  overflow-y: scroll;
  scroll-behavior: smooth;
}

.vipWrap {
  padding: 40rpx 0 0;
  /* width: 702rpx; */
  width: 100%;
  overflow-x: scroll;
  scroll-behavior: smooth;
}

.henWrap {
  display: inline-flex;
}

.vipWrap .item {
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  width: 204rpx;
  height: 232rpx;
  background: #ffffff;
  box-shadow: 0px 2rpx 40rpx 0px rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  overflow: hidden;
  margin-right: 22rpx;
  box-sizing: border-box;
}

.vipWrap .item:nth-of-type(1) {
  margin-left: 24rpx;
}

.vipWrap .item:last-child {
  margin-right: 22rpx;
}

.vipWrap .item .gg {
  position: absolute;
  width: 36rpx;
  height: 36rpx;
  right: 0;
  top: 0;
  opacity: 0;
}

.vipWrap .item .one {
  font-size: 28rpx;
  font-weight: 400;
  color: #20263a;
  padding-top: 32rpx;
  text-align: center;
}

.vipWrap .item .two {
  font-size: 28rpx;
  font-weight: 600;
  color: #20263a;
  padding-top: 20rpx;
}

.vipWrap .item .two text {
  font-weight: bold;
  font-size: 40rpx;
}

.vipWrap .item .three {
  font-size: 24rpx;
  font-weight: 400;
  color: #c7975b;
  padding-top: 18rpx;
}

.henWrap .lactive {
  background: #fcf9f2;
  border: 4rpx solid #eac89e;
}

.henWrap .lactive .one {
  color: #573709;
}

.henWrap .lactive .two {
  color: #573709;
}

.vipWrap .lactive .gg {
  opacity: 1;
}

/* 支付方式  */

.paytype {
  background: #fff;
  padding: 0 24rpx;
}

.paytype .tit {
  height: 98rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #74798c;
}

.paycont {
  display: flex;
  justify-content: space-between;
}

.paycont .item {
  display: flex;
  justify-content: space-between;
  padding: 0 16rpx;
  align-items: center;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  opacity: 1;
  border: 1rpx solid #dedede;
  height: 104rpx;
  width: 316rpx;
}

.paycont .item .item-l {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #20263a;
  flex: 1;
}

.paycont .pactive {
  background: #fcf9f2;
  border: 2rpx solid #eac89e;
}

.paycont .item .item-l image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.item-r {
  width: 32rpx;
  height: 32rpx;
}

.item-r image {
  width: 100%;
  height: 100%;
}

/*       <!-- vip专属特权 --> */
.special {
  padding-bottom: 120rpx;
}

.special .tit {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-family:
    PingFang SC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: #573709;
  width: 100%;
  padding-top: 40rpx;
  margin-bottom: 20rpx;
}

.special .tit view {
  padding: 0 24rpx;
}

.special .tit .img1 {
  width: 46rpx;
  height: 17.32rpx;
}

.special .tit .img2 {
  width: 46rpx;
  height: 17.32rpx;
}

.slist {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 24rpx 8rpx;
}

.slist .itms {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 25%;
  height: 152rpx;
  margin-bottom: 32rpx;
}

.itms image {
  width: 80rpx;
  height: 80rpx;
}

.itms view:nth-of-type(1) {
  font-size: 24rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #20263a;
  padding-top: 8rpx;
}

.itms view:nth-of-type(2) {
  font-size: 20rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #c7975b;
}

.paybtn {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 654rpx;
  height: 96rpx;
  background: linear-gradient(225deg, #2b2f3d 0%, #2c303b 100%);
  box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(1, 1, 1, 0.2);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  font-family:
    PingFang SC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: #eec895;
  z-index: 10;
}

.paybtn .second-view {
  font-size: 24rpx;
  line-height: 36rpx;
}
