import dayjs from 'dayjs';
let dateTagList = []; // 日期tag列表
const bolAry = [{
    id: 'true',
    name: '有'
  },
  {
    id: 'false',
    name: '无'
  }
]
// 时间戳转日期
function transformDate(num) {
  return dayjs(num).format('YYYY-MM-DD');
}
// 计算日期
function calcDate(start, end) {
  const now = dayjs();
  const oneYearAgoStart = now.subtract(end, 'year');
  const oneYearAgoEnd = now.subtract(start, 'year');

  const startDateStr = end ? transformDate(oneYearAgoStart) : '';
  const endDateStr = transformDate(oneYearAgoEnd);

  const dateCombination = `${startDateStr}$${endDateStr}`;
  dateTagList.push(dateCombination);
  return dateCombination;
}

let searchLeftLists = [{
    title: '基础筛选',
    map: {
      ent_name: '企业名称',
      trade_types: '所属行业',
      areas: '所在地区'
    }
  },
  {
    title: '产业优选',
    map: {
      head_ent_flag: '龙头企业',
      ent_expand: '疑似扩张',
      ent_size: '企业规模',
      benefits_assess: '效益评估',
      industrial_list: '新兴产业',
      // classic_industry_code_list: '经典产业',
      technology: '科技型企业'
    }
  },
  {
    title: '企业相关',
    map: {
      est_date: '注册时间',
      reg_capital: '注册资本',
      ent_status_list: '企业状态',
      enterprise_license_list: '企业许可',
      ent_type: '企业类型',
      ent_entity_type: '实体类型'
    }
  },
  {
    title: '经营状态',
    map: {
      mobile_phone_flag: '联系方式',
      fixed_phone_flag: '手机号码',
      email_flag: '联系邮箱',
      // "": "实缴资本",
      insured_num: '从业人数',
      financing_info_list: '融资信息',
      listing_status: '上市状态', //22.10.30
      tendering_and_bidding: '招投标',
      job_flag: '招聘',
      // "": "建筑资质",
      // "": "资质证书",
      tax_credit_flag: '纳税信用'
      // "": "进出口信用",
      // "land": "国有土地受让"
    }
  },
  {
    title: '知识产权',
    map: {
      trade_mark_info_flag: '商标信息',
      patent_info: '专利信息',
      copyrights_flag: '软件著作权',
      copyright_of_works: '作品著作权',
      official_website_info: '官网信息',
      icp_filing: 'ICP备案',
      android_app: '安卓APP',
      apple_app: '苹果APP',
      applet: '小程序',
      we_chat_official_account: '微信公众号',
      weibo_flag: '微博'
    }
  },
  {
    title: '风险信息',
    map: {
      dishonest_info: '失信信息',
      // "": "破产重整",
      judgment_doc: '裁判文书',
      administrative_penalty: '行政处罚',
      // "": "清算信息",
      // "": "环保处罚",
      chattel_mortgage: '动产抵押',
      business_abnormalities: '经营异常'
      // "": "股权冻结"
    }
  }
];
let searchTermList = [{
    title: '所在地区',
    type: 'areas',
    special: 'pop',
    content: ''
  },
  {
    title: '所属行业',
    type: 'trade_types',
    special: 'pop',
    content: ''
  },
  {
    title: '企业规模',
    type: 'ent_size',
    icon: true,
    vip: true,
    list: [{
        id: 'S',
        name: 'S'
      },
      {
        name: 'A+',
        id: 'APLUS'
      },
      {
        id: 'A',
        name: 'A'
      },
      {
        name: 'B+',
        id: 'BPLUS'
      },
      {
        id: 'C+',
        name: 'C'
      },
      {
        id: 'D',
        name: 'D'
      },
      {
        name: 'E+',
        id: 'EPLUS'
      },
      {
        id: 'E',
        name: 'E'
      }
    ]
  },
  {
    title: '科技型企业',
    type: 'technology',
    isOpenIcon: true,
    isOpen: true,
    vip: true,
    list: [{
        id: 'HN',
        name: '高新技术企业'
      },
      {
        id: 'MST',
        name: '科技型中小企业'
      },
      {
        id: 'G',
        name: '瞪羚企业'
      },
      {
        id: 'SME',
        name: '专精特新-专精特新中小企业'
      },
      {
        id: 'CMI',
        name: '专精特新-制造业单项冠军'
      },
      {
        id: 'SG',
        name: '专精特新-专精特新小巨人'
      },
      {
        id: 'U',
        name: '独角兽企业'
      },
      {
        id: 'GT',
        name: '科技小巨人'
      },
    ]
  },
  {
    title: '注册年限',
    type: 'est_date',
    min: '最低年限',
    max: '最高年限',
    unit: '年',
    genre: 'tpop', //区分类型
    list: [{
        id: calcDate(0, 1),
        name: '1年内'
      },
      {
        id: calcDate(1, 2),
        name: '1-2年'
      },
      {
        id: calcDate(2, 3),
        name: '2-3年'
      },
      {
        id: calcDate(3, 5),
        name: '3-5年'
      },
      {
        id: calcDate(5, 0),
        name: '5年以上'
      }
    ]
  },
  {
    title: '注册资本',
    type: 'reg_capital',
    min: '最低资本',
    max: '最高资本',
    unit: '万元',
    genre: 'input', //区分类型
    list: [{
        id: '0$100',
        name: '100万以下'
      },
      {
        id: '100$200',
        name: '100-200万'
      },
      {
        id: '500$1000',
        name: '500-1000万'
      },
      {
        id: '1000$5000',
        name: '1000-5000万'
      },
      {
        id: '5000$',
        name: '5000万以上'
      }
    ]
  },
  {
    title: '企业状态', //企业经营状态
    type: 'ent_status_list',
    list: [{
        id: '1',
        name: '在营'
      },
      {
        id: '2',
        name: '吊销'
      },
      {
        id: '3',
        name: '注销'
      },
      {
        id: '9',
        name: '其他'
      }
    ]
  },
  {
    title: '实体类型',
    type: 'ent_entity_type',
    list: [{
        id: '1',
        name: '工商'
      },
      {
        id: '3',
        name: '个体'
      }
    ]
  },
  {
    title: '企业类型',
    type: 'ent_type',
    special: 'pop',
    content: ''
  },
  {
    title: '企业许可',
    type: 'enterprise_license_list',
    special: 'pop',
    content: '',
    vip: true
  },
  {
    title: '手机号码',
    type: 'mobile_phone_flag', //到时候这个需要和下面那个合并成 fixed_phone_flag
    vip: true,
    list: [...bolAry]
  },
  {
    title: '固定电话',
    type: 'fixed_phone_flag',
    list: [...bolAry]
  },
  {
    title: '企业名称',
    type: 'ent_name', //到时候这个需要和下面那个合并成 fixed_phone_flag
    special: 'input',
    content: ''
  },
  {
    title: '联系邮箱',
    type: 'email_flag',
    list: [...bolAry]
  },
  {
    title: '从业人数',
    type: 'insured_num',
    list: [{
        id: '0$49',
        name: '0-49'
      },
      {
        id: '50$99',
        name: '50-99'
      },
      {
        id: '100$499',
        name: '100-499'
      },
      {
        id: '500$999',
        name: '500-999'
      },
      {
        id: '1000$4999',
        name: '1000-4999'
      },
      {
        id: '5000$',
        name: '5000人以上'
      }
    ]
  },
  {
    title: '融资信息',
    type: 'financing_info_list',
    isOpen: true,
    isOpenIcon: true,
    vip: true,
    list: [{
        id: 'AW',
        name: '天使轮'
      },
      {
        id: 'A',
        name: 'A轮'
      },
      {
        id: 'B',
        name: 'B轮'
      },
      {
        id: 'C',
        name: 'C轮'
      },
      {
        id: 'D',
        name: 'D轮'
      },
      {
        id: 'EPLUS',
        name: 'E轮及以上'
      },
      {
        id: 'IPO',
        name: 'IPO'
      },
      {
        id: 'F',
        name: '定向增发'
      },
    ]
  },
  {
    title: '招投标',
    type: 'tendering_and_bidding',
    vip: true,
    list: [...bolAry]
  },
  {
    title: '招聘',
    type: 'job_flag',
    vip: true,
    list: [...bolAry]
  },
  {
    title: '纳税信用',
    type: 'tax_credit_flag',
    vip: true,
    list: [{
        id: 'true',
        name: 'A级'
      },
      {
        id: 'fasle',
        name: '非A级'
      }
    ]
  },
  {
    title: '商标信息',
    type: 'trade_mark_info_flag',
    vip: true,
    list: [...bolAry]
  },
  {
    title: '专利信息',
    vip: true,
    type: 'patent_info',
    list: [{
        id: '4',
        name: '发明授权'
      },
      {
        id: '1',
        name: '发明公告'
      },
      {
        id: '2',
        name: '实用新型'
      },
      {
        id: '3',
        name: '外观设计'
      }
    ]
  },
  {
    title: '安卓APP',
    type: 'android_app',

    list: [...bolAry]
  },
  {
    title: '苹果APP',
    type: 'apple_app',

    list: [...bolAry]
  },
  {
    title: '小程序',
    type: 'applet',

    list: [...bolAry]
  },
  {
    title: '微信公众号',
    type: 'we_chat_official_account',

    list: [...bolAry]
  },
  {
    title: '微博',
    type: 'weibo_flag',

    list: [...bolAry]
  },
  {
    title: '失信信息',
    type: 'dishonest_info',
    vip: true,
    list: [...bolAry]
  },
  {
    title: '裁判文书',
    vip: true,
    type: 'judgment_doc',
    list: [...bolAry]
  },
  {
    title: '行政处罚',
    vip: true,
    type: 'administrative_penalty',
    list: [...bolAry]
  },
  {
    title: '动产抵押',
    vip: true,
    type: 'chattel_mortgage',
    list: [...bolAry]
  },
  {
    title: '经营异常',
    vip: true,
    type: 'business_abnormalities',
    list: [...bolAry]
  },
  {
    title: '软件著作权',
    vip: true,
    type: 'copyrights_flag',
    list: [...bolAry]
  },
  {
    title: '作品著作权',
    vip: true,
    type: 'copyright_of_works',
    list: [...bolAry]
  },
  {
    title: '官网信息',
    type: 'official_website_info',
    list: [...bolAry]
  },
  {
    title: 'ICP备案',
    type: 'icp_filing',
    list: [...bolAry]
  },

  {
    title: '龙头企业',
    type: 'head_ent_flag',
    vip: true,
    list: [{
        id: 'true',
        name: '是龙头企业'
      },
      {
        id: 'false',
        name: '非龙头企业'
      }
    ]
  },
  {
    title: '疑似扩张',
    vip: true,
    type: 'ent_expand',
    list: [{
        id: 'A',
        name: '近期融资'
      },
      {
        id: 'B',
        name: ' 近期新增分支机构'
      },
      {
        id: 'C',
        name: '近期疑似扩张'
      },
      {
        id: 'D',
        name: '近期有对外投资'
      },
      {
        id: 'E',
        name: '近期新增专利公示'
      },
      {
        id: 'F',
        name: '近期新增专利申请'
      }
    ]
  },
  {
    title: '上市状态',
    vip: true,
    type: 'listing_status',
    list: [{
        id: 'A',
        name: 'A股'
      },
      {
        id: 'B',
        name: 'B股'
      },
      {
        id: 'NTB',
        name: '新三板'
      },
      {
        id: 'HK',
        name: '港股'
      },
      {
        id: 'STAR',
        name: '科创板'
      },
      {
        id: 'USA',
        name: '美股'
      },
      {
        id: 'NONE',
        name: '非上市'
      }
    ]
  },
  {
    title: '效益评估',
    type: 'benefits_assess',
    list: [{
        id: 'S',
        name: 'S'
      },
      {
        name: 'A+',
        id: 'APLUS'
      },
      {
        id: 'A',
        name: 'A'
      },
      {
        name: 'B+',
        id: 'BPLUS'
      },
      {
        id: 'C+',
        name: 'C'
      },
      {
        id: 'D',
        name: 'D'
      },
      {
        name: 'E+',
        id: 'EPLUS'
      },
      {
        id: 'E',
        name: 'E'
      }
    ]
  },
  // ps这里有点特俗
  {
    title: '新兴产业',
    type: 'industrial_list',
    vip: true,
    special: 'mpop',
    content: ''
  },
  // {
  //   title: '经典产业链',
  //   type: 'classic_industry_code_list',
  //   vip: true,
  //   special: 'pop',
  //   content: ''
  // },
];
module.exports = {
  dateTagList,
  searchLeftLists,
  searchTermList
};