// companyPackage/pages/addCard/components/formItem/formItem.js
import request from '../../../../../service/request';
import {chainUrl} from '../../../../../service/config';
import {debounce} from '../../../../../utils/formate';
var QQMapWX = require('../../../../qqmap/qqmap-wx-jssdk.min.js');
var qqmapsdk = new QQMapWX({
  key: 'VLXBZ-7CBCV-AGIPH-UQKQ2-KVCWQ-CBBK6' // 必填
});
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 名片类型
    cardType: {
      type: String
    },
    //表单配置
    config: {
      type: Object
    },
    //值
    value: {
      type: null, // 允许任意类型
      observer(newVal) {
        let config = this.properties.config;
        // this.data.regionvisible = false
        if (config.type === 'listSelect') {
          this.setData({
            listSelectValue: newVal
          });
        } else if (config.type === 'district') {
          this.getCityInfo(newVal);
          // this.data.regionvisible = true
        } else if (config.type === 'interval') {
          console.log('newVal', newVal);
          this.setData({
            min_seat: newVal[0],
            max_seat: newVal[1]
          });
        }
      }
    },
    //单位值
    unitValue: {
      type: String
    },
    city: {
      type: String
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    visible: false,
    regionvisible: false,
    //缓存 listSelect 值
    listSelectValue: [],
    //缓存地区值
    districtData: [],
    focus: false,
    keyword: '',
    // 缓存模糊地址
    suggestion: [],
    min_seat: '',
    max_seat: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    regionClose() {},
    //获取地区编码的层级关系
    getCityInfo(code) {
      let url = `${chainUrl}/search/transform/${code}`,
        that = this;
      let params = {url, method: 'GET'};
      request(params).then(res => {
        let item = res[0],
          districtData = [];
        districtData = that.formartDistrict(item);
        this.setData({
          districtData
        });
      });
    },

    //格式化地区
    formartDistrict(data) {
      let arr = [];
      function flatten(obj) {
        let {code, name, parent} = obj;
        arr.push({code, name});
        if (parent) {
          flatten(parent);
        }
      }
      flatten(data);
      arr = arr.reverse();
      return arr;
    },
    //输入框值
    inputChange(e) {
      let value = e.detail.value;
      let {code, inputtype} = this.properties.config;
      if (inputtype && inputtype === 'litnumber') {
        let val = value;
        var reg = /[^\d.]/g;
        val = val.replace(reg, '');
        val = val.replace(/^\./g, '');
        val = val.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
        val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        this.triggerEvent('change', {[code]: val});
      } else if (inputtype && inputtype === 'number') {
        let int = value;
        int = int.replace(/^(0+)|[^\d]+/g, '');
        this.triggerEvent('change', {[code]: int});
      } else if (inputtype && inputtype === 'cetext') {
        let ce = value;
        ce = ce.replace(/[^a-zA-Z\u4e00-\u9fa5]/g, '');
        this.triggerEvent('change', {[code]: ce});
      } else {
        this.triggerEvent('change', {[code]: value});
      }
    },
    inputSearch(e) {
      let value = e.detail.value;
      let {code} = this.properties.config;
      this.triggerEvent('change', {[code]: value});
    },
    focusAddress() {
      this.setData({focus: true});
    },
    blurAddress() {
      // this.setData({focus:false,suggestion:[]})
    },
    //触发关键词输入提示事件
    getsuggest: debounce(function ({...e}) {
      var _this = this;
      let keyword = e[0].detail.value;
      //调用关键词提示接口
      qqmapsdk.getSuggestion({
        //获取输入框值并设置keyword参数
        keyword, //用户输入的关键词，可设置固定值,如keyword:'KFC'
        //region:'北京', //设置城市名，限制关键词所示的地域范围，非必填参数
        success: function (res) {
          //搜索成功后的回调
          if (res.count === 0) {
            console.log('结果为空');
            wx.showToast({
              title: '请输入详细地址',
              icon: 'none'
            });
            let {code} = _this.properties.config;
            _this.triggerEvent('change', {
              [code]: ''
            });
          }
          var sug = [];
          for (var i = 0; i < res.data.length; i++) {
            sug.push({
              // 获取返回结果，放到sug数组中
              title: _this.arrfiy(keyword, res.data[i].title),
              id: res.data[i].id,
              addr: res.data[i].address,
              city: res.data[i].city,
              district: res.data[i].district,
              latitude: res.data[i].location.lat,
              longitude: res.data[i].location.lng
            });
          }
          _this.setData({
            //设置suggestion属性，将关键词搜索结果以列表形式展示
            suggestion: sug,
            keyword
          });
        },
        fail: function (error) {
          _this.setData({
            suggestion: []
          });
          console.error(error);
        },
        complete: function (res) {
          console.log(res);
        }
      });
    }, 1000),
    arrfiy(key, chain) {
      return chain.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
    },
    clickItem(e) {
      let {item} = e.currentTarget.dataset;
      let value = e.currentTarget.dataset.item.addr;
      let {code} = this.properties.config;
      this.triggerEvent('change', {
        [code]: value,
        lat: item.latitude,
        lng: item.longitude
      });
      this.setData({focus: false});
    },

    // 下拉选择值
    selectValue(e) {
      let {item} = e.currentTarget.dataset;
      let {code, type, unitCode} = this.properties.config;
      //单位选择器 ， 单独处理
      if (type === 'inputSelect') {
        this.triggerEvent('change', {
          [unitCode]: item.code
        });
      } else {
        this.triggerEvent('change', {
          [code]: item.code
        });
      }
      this.hideMask();
    },

    //区间最小值改变
    minChange(e) {
      let data = e.detail.value;
      data = data.replace(/^(0+)|[^\d]+/g, '');
      let value = this.properties.value;
      let {code} = this.properties.config;
      let max = value && value[1] ? value[1] : '';
      this.triggerEvent('change', {
        [code]: [data, max]
      });
    },

    //区间最大值改变
    maxChange(e) {
      let data = e.detail.value;
      data = data.replace(/^(0+)|[^\d]+/g, '');
      let value = this.properties.value;
      let {code} = this.properties.config;
      let min = value && value[0] ? value[0] : '';
      this.triggerEvent('change', {
        [code]: [min, data]
      });
    },

    //区域地区选择
    districtChange(e) {
      let data = e.detail;
      let arr = [];
      data.forEach(item => {
        arr.push(item.name);
      });
      this.setData({
        districtData: data
      });
      this.triggerEvent('change', {
        region: data[data.length - 1].code,
        region_name: arr.join('·')
      });
    },

    listSelectValue(e) {
      let {item} = e.currentTarget.dataset;
      // let value = this.properties.value || [];
      // var newArr = value.concat(this.data.listSelectValue)
      // console.log('value', value, newArr);
      // console.log('this.data.listSelectValue', this.data.listSelectValue);
      // var listSelectValue = [];
      // for (var i = 0; i < newArr.length; i++) {
      //   if (listSelectValue.indexOf(newArr[i]) < 0) { // 表示新数组里没有这个元素
      //     listSelectValue.push(newArr[i]);
      //   }
      // }

      let listSelectValue = [...this.data.listSelectValue];
      let _has = listSelectValue.some(val => val === item.code);
      if (_has) {
        listSelectValue = listSelectValue.filter(val => val !== item.code);
      } else {
        listSelectValue.push(item.code);
      }
      // this.properties.value = listSelectValue
      this.setData({
        listSelectValue
      });
    },

    submit() {
      let {code, type} = this.properties.config;
      if (type === 'listSelect') {
        this.triggerEvent('change', {
          [code]: this.data.listSelectValue
        });
      } else if (type === 'district') {
        let {districtData} = this.data,
          length = districtData.length;
        this.triggerEvent('change', {
          [code]: districtData[length - 1].code
        });
      }
    },

    close() {
      let {code, type} = this.properties.config;

      if (type === 'listSelect') {
        this.setData({
          listSelectValue: this.properties.value
        });
      }
    },

    //关闭弹框
    hideMask() {
      this.setData({
        visible: false
      });
    },
    // 打开弹框
    showMask(e) {
      if (e.currentTarget.dataset.type === 'district') {
        this.setData({
          regionvisible: true
        });
      }
      this.setData({
        regionvisible: true,
        visible: true
      });
    }
  }
});
