import constant from '../../utils/constant';
import {
  chain 
} from '../../service/api';

Component({
  properties: {
    // 弹出层显示/隐藏
    visible: {
      type: Boolean,
      value: false
    },
    position: {
      type: String,
      value: "bottom",
    },
    // 上一次选中的数据，用于回填
    oldData: {
      type: Array,
      value: []
    },
    dataType: {
      type: String,
      value: constant.Industrial
    },
    mark: {
      type: String,
      value: ''
    }
  },
  data: {

  },
  methods: {
    async getAllChainData() {
      let allChainData = wx.getStorageSync(this.dataType) || false;
      const tempData = [] // 只取第一级和第二级 作为扁平化数据
      if (!allChainData) {
        allChainData = await chain.xinChainAll();
        console.log('222', allChainData);
        allChainData = allChainData.forEach(chain => {
          tempData.push({
            code: chain.chain_code,
            name: chain.name,
            isActive: false
          })
          chain.children.forEach(child => {
            tempData.push({
              code: child.chain_code,
              name: child.name,
              isActive: false
            })
          })
        })
      }
      console.log(222, tempData);
      wx.setStorageSync(this.dataType, tempData)
      this.setData({
        sourceData: tempData
      });
    },
  },
  pageLifetimes: {
    show: function () {
      this.getAllChainData()
    },
  },
})