import constant from '../../utils/constant';
import {
  chain
} from '../../service/api';

Component({
  properties: {
    // 弹出层显示/隐藏
    visible: {
      type: Boolean,
      value: false,
      observer(newVal) {
        if (newVal) {
          this.initData();
        }
      }
    },
    // 弹窗位置
    position: {
      type: String,
      value: 'bottom'
    },
    // 弹窗标题
    title: {
      type: String,
      value: '请选择'
    },
    // 上一次选中的数据，用于回填
    oldData: {
      type: Array,
      value: []
    },
    // 数据类型，用于缓存key
    dataType: {
      type: String,
      value: constant.Industrial
    },
    // 标识符，用于事件回调
    mark: {
      type: String,
      value: ''
    }
  },

  data: {
    sourceData: [], // 原始数据列表
    selectedItems: [] // 已选中的项目
  },

  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      await this.getAllChainData();
      this.setSelectedItems();
    },

    /**
     * 获取产业链数据
     */
    async getAllChainData() {
      try {
        let allChainData = wx.getStorageSync(this.data.dataType);

        if (!allChainData || allChainData.length === 0) {
          // 从API获取数据
          const {
            chain_data
          } = await chain.xinChainAll();
          const tempData = []; // 扁平化数据，只取第一级和第二级
          if (Array.isArray(chain_data)) {
            chain_data.forEach(chainItem => {
              // 添加一级数据
              tempData.push({
                code: chainItem.chain_code,
                name: chainItem.name,
                active: false
              });

              // 添加二级数据
              if (chainItem.children && Array.isArray(chainItem.children)) {
                chainItem.children.forEach(child => {
                  tempData.push({
                    code: child.chain_code,
                    name: child.name,
                    active: false
                  });
                });
              }
            });
            allChainData = tempData
          }
          console.log(11, this.data.dataType, allChainData);
          wx.setStorageSync(this.data.dataType, allChainData);
        }

        this.setData({
          sourceData: allChainData
        });
      } catch (error) {
        console.error('获取产业链数据失败:', error);
      }
    },

    /**
     * 设置选中项（回填数据）
     */
    setSelectedItems() {
      const {
        oldData,
        sourceData
      } = this.data;

      if (!oldData || oldData.length === 0) {
        return;
      }

      // 创建选中项的code集合
      const selectedCodes = new Set();
      oldData.forEach(item => {
        if (item.active || item.status === 'checked') {
          selectedCodes.add(item.code);
        }
      });

      // 更新sourceData中的选中状态
      const updatedSourceData = sourceData.map(item => ({
        ...item,
        active: selectedCodes.has(item.code)
      }));

      // 获取选中的项目
      const selectedItems = updatedSourceData.filter(item => item.active);

      this.setData({
        sourceData: updatedSourceData,
        selectedItems
      });
    },

    /**
     * 点击列表项
     */
    handleItemClick(e) {
      const {
        item
      } = e.currentTarget.dataset;
      const {
        sourceData
      } = this.data;

      // 切换选中状态
      const updatedSourceData = sourceData.map(dataItem => {
        if (dataItem.code === item.code) {
          return {
            ...dataItem,
            active: !dataItem.active
          };
        }
        return dataItem;
      });

      // 获取选中的项目
      const selectedItems = updatedSourceData.filter(item => item.active);

      this.setData({
        sourceData: updatedSourceData,
        selectedItems
      });
    },

    /**
     * 提交选择
     */
    submit() {
      const {
        selectedItems,
        mark
      } = this.data;

      // 转换为符合接口要求的格式
      const checkedList = selectedItems.map(item => ({
        code: item.code,
        name: item.name,
        status: 'checked',
        active: true
      }));

      this.triggerEvent('submits', {
        checkedList,
        mark
      });
    },

    /**
     * 关闭弹窗
     */
    close() {
      const {
        mark
      } = this.data;

      // 关闭时也要触发事件，保持原有数据
      this.triggerEvent('submits', {
        checkedList: this.data.oldData,
        mark
      });
    }
  },

  pageLifetimes: {
    show: function () {
      // 页面显示时初始化数据
      if (this.data.visible) {
        this.initData();
      }
    }
  }
});