/**
 * 样式混入定义
 * 提供可复用的样式混入
 */

/* 边框混入 */
@mixin border-bottom {
  content: " ";
  width: 100%;
  height: 1px;
  background: $border-color;
  position: absolute;
  bottom: 0;
  left: 0;
  transform: scaleY(0.5);
}

@mixin border-top {
  content: " ";
  width: 100%;
  height: 1px;
  background: $border-color;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

/* 菜单角落装饰 */
@mixin menu-corner($position) {
  position: absolute;
  content: "";
  right: 0;
  width: 26rpx;
  height: 26rpx;
  background-size: contain;
  background-repeat: no-repeat;

  @if $position == top {
    top: -26rpx;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAMlJREFUSEvt1SEOAjEQheF/EwQSQQICsQKxAoFE4DjCHmEFEssBcBgk90AgkUiOwTEeGTKbEEICIe0IQpPafn3TTKcgaBVBDn/o60r/VukkdUMSSepFQf0oqIyCJlHQIgpqoqBtFHTKDlkPAdcIaAnss0L29QAXoMoNrYCdffnZIEmVp7FUeSBJQ+AMlO0AS57IkSMwfZySSSEv1wEYP4/iJJCkDmAPvwHub5IU8masgfWrFB+VznugvZ11t+0BMPJDZ8AcsDRv1w3mV0k0AXl2AAAAAABJRU5ErkJggg==");
  } @else if $position == bottom {
    bottom: -26rpx;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAANlJREFUSEvt1jEKwkAQheE/IJhCMKVlCkHLFBbewCvYp7AVPIQWHsF7aGfhBew8hIcYGZiVCK6CZqcQA4GwDPPxZkmyGZFLRLpADnSAnt0DoATGwASYWk2szX09e1vxokBE+sAcWBoerf4KCl1FRJMvgE0sYStQAxwBBxvvQ7pWIe0sIrqPe6BqSq1DDewEDAOWBDJMx3gOe5YMMmwFbPU5NaTv4EVHmBSyVDWw84AK4JocslRHL2jtBdVe0MwLqryg0gsqfg7KXRIl/6gm//E9O6H8R/fxMfAGrshJOEGy/j4AAAAASUVORK5CYII=");
  }
}

/* 激活指示器 */
@mixin active-indicator {
  position: absolute;
  bottom: 14rpx;
  left: 52rpx;
  content: "";
  width: 72rpx;
  height: 6rpx;
  background: linear-gradient(90deg, rgba(231, 36, 16, 0.99) 26%, rgba(231, 36, 16, 0.5) 98%);
  border-radius: 2rpx 2rpx 2rpx 2rpx;
  opacity: 0.7;
}

/* 子菜单激活指示器 */
@mixin child-active-indicator {
  content: "";
  position: absolute;
  bottom: 14rpx;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 32rpx;
  background: linear-gradient(180deg, rgba(231, 36, 16, 0.99) 0%, #f17b6f 74%);
}

/* 章节标题样式 */
@mixin section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-lg;
  font-family:
    PingFang SC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: $text-color-primary;

  .line-left {
    width: 60rpx;
    height: 1rpx;
    background: rgba(155, 158, 172, 1);
    margin-right: $spacing-md;
    transform: scaleY(0.5);
  }

  .line-right {
    width: 60rpx;
    height: 1rpx;
    background: rgba(155, 158, 172, 1);
    margin-left: $spacing-md;
    transform: scaleY(0.5);
  }
}

/* 标签样式 */
@mixin tag-style {
  padding: 0 26rpx;
  height: $height-input;
  line-height: $height-input;
  background-color: $tag-background;
  margin-right: $spacing-md;
  margin-top: 24rpx;
  border-radius: $border-radius-small;
  font-weight: 400;
  cursor: pointer;
  transition: all $transition-fast ease;
}

@mixin tag-active-style {
  color: #ffffff;
  background: $tag-background-active;
}

/* 输入框包装样式 */
@mixin input-wrap-style {
  display: flex;
  align-items: center;
  margin-top: 25rpx;
}

@mixin input-item-style {
  display: flex;
  width: 350rpx;
  height: $height-input;
  padding: 0 19rpx;
  background-color: $input-background;
  border-radius: $border-radius-small;
  margin-right: 19rpx;
  color: $text-color-placeholder;
  transition: all $transition-fast ease;
}

@mixin input-active-style {
  color: $primary-color !important;
  background-color: $input-background-active;
}

/* VIP 徽章样式 */
@mixin vip-badge {
  width: 64rpx;
  height: 32rpx;
  margin-left: $spacing-xs;
  background: url("data:image/png;base64,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")
    no-repeat;
  background-size: 100% 100%;
}

/* 信息图标样式 */
@mixin info-icon {
  display: inline-block;
  margin-left: $spacing-sm;
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAAXNSR0IArs4c6QAAANhQTFRFAAAAgICAn5+flZWqn5+vmZmzn5+1m5u2paW9n5+3oKi9nKO4o6O+n5+5o6O4o6i4oKW5oqe5oqa6n6O3oqW7oKO7oaW4oaW7oKa7oaS5oKW5oKW8oaS5oKW5oaO6oaa6oKS6oaW6oKS6oaW7oKW5oKS5oKa5oKS6oKW6oKW6oKS6oKW6oKS6oKW6oKS5oKW5oKS5oKW5oKS5oKS6oKW6oKW6oKS6oKW5oKW6oKS5oKS6oKW5oKW6oKS5oKW5oKW5oKS5oKS6oKW6oKW6oKS6oKW6oKW6oKW66g9uAAAAAEd0Uk5TAAQIDBAUGBwfICMkJygvLzM3P0BHS09PU1dbW19jZ2drb3N/i4+Pl5ebn5+jp6uvs7O3x8fLz8/P09PX19vf4+fr6/P39/vWY4YUAAABYklEQVQ4y32T2VrCMBBGxxIXrLivFVxBhSgoWhCLMWDsef838qaFtrbO1SwnmfmSf0QWpgI9nsM87AZK/tqm/mFh7n6rUPbaMRBHL1oPJgCu4+WOT4HX1mrSqzkGoo1lfceCOcyeODZgd9Now0K/Vuj5BHYz8afQ+Tt1ByIv9QZpdu/6ej/1+9AWEdlymMXEt5nLaobYF5F7OJYyQA5BiyjHeNn4YjS6WUZvOCUBtKTCWhBIF9aqgNWYnoSYTKp+cOBnwiljmTPMZG7zT/LCXEBXAxoEHv8H/m0xwMooN2QB+CAUDetVgIKunMNlFdCEM1GOjyrgHadEenBaDpzAg4j4MV+qDPAMriEi0obnlSS53WwulPiUwl5UKrk7mCRC8i08F0Rb64Otp9GuBXOSrR8ZsI3ML0fA+1UijLWLEJjUc2vQcQBmqB+HnwBx2yvM1Hhwy+X97vklAlNBN5zBLL/+v69tTgQXnGC4AAAAAElFTkSuQmCC");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* 响应式混入 */
@mixin mobile-only {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: 769px) {
    @content;
  }
}

/* 文本省略混入 */
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* 居中混入 */
@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin center-absolute {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 清除浮动混入 */
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

/* 动画混入 */
@mixin fade-in($duration: $transition-fast) {
  animation: fadeIn $duration ease-in-out;
}

@mixin slide-up($duration: $transition-fast) {
  animation: slideUp $duration ease-out;
}

/* 关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 滚动条样式混入 */
@mixin custom-scrollbar($width: 6rpx, $track-color: #f1f1f1, $thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;

    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}
