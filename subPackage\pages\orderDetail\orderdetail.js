import { mine } from '../../../service/api';
import { formatDate } from '../../../utils/formate';
import { getOderId } from '../../../utils/pay';
import { formatterParams } from '../../../utils/util';
const payConstant = {
    'Wechat_Mini': '微信',
    'Wechat': '微信',
    'Alipay': '支付宝',
    'UnionPay': '银联',
}
const statusConstant = {
    'UNPAID': '待支付',
    'FAITH': '待支付', // 支付失败，页面状态与待支付保持一致
    'SUCCESS': '已支付',
    'CLOSED': '已关闭',
    'EXPIRED': '已关闭', // 已过期，页面状态与已关闭保持一致
}
const ORDER_TYPE_ENUM = [{
  code: '',
  name: '全部订单',
},
{
  code: '0',
  name: '普通订单',
},
{
  code: '1',
  name: '个人VIP订单',
},
{
  code: '2',
  name: '团队VIP订单',
}];
const ORDER_STATUS_ENUM = [
  {
    code: '',
    name: '全部状态',
  },
  {
    code: 'SUCCESS',
    name: '已支付',
  },
  {
    code: 'UNPAID$FAITH',
    name: '待支付',
  },
  {
    code: 'CLOSED$EXPIRED',
    name: '已关闭',
  }
]
const app = getApp()
Page({
    data: {
      selectData: [
        {
          code: 'vip_type',
          name: '订单类型',
          defaultValue: '',
          type: 'radio',
          children: ORDER_TYPE_ENUM
        },
        {
          code: 'payment_status_list',
          defaultValue: '',
          name: '订单状态',
          type: 'radio',
          children: ORDER_STATUS_ENUM
      }],
      bazaarParms: {
          $filter: '',
          $count: true,
          $offset: 0, //偏移量
          $limit: 10 //每页多少条
      },
      bazaarlist: [], //获取列表
      bazaarIsFlag: true, //节流 true允许
      bazaarHasData: true, //  是否还有数据
      bazaarIsNull: false, // list长度是否为0
      bazaarIsTriggered: false, // 下拉刷新状态
    },
    onShow() {
        this.init()
    },
    init() {
        app.showLoading()
        this.initAllParms()
        this.initBazaar(() => {
            wx.hideLoading()
        })
    },
    initAllParms() {
      const { bazaarParms } = this.data;
        let obj = {
            bazaarParms: {
              ...bazaarParms,
              $offset: 0,
            },
            bazaarlist: [],
            bazaarIsFlag: true,
            bazaarHasData: true,
            bazaarIsNull: false,
            bazaarIsTriggered: false,
            mybuslist: [],
        }
        this.setData(obj)
    },
    // 筛选框回调--发送请求 
    onSelect(val) {

      let { selectData } = this.data
      // this.setData({ bazaarParms, bazaarlist }, () => {
      //     app.showLoading('加载中...')
      //     this.initBazaar(() => wx.hideLoading())
      // })
      const key = Object.keys(val.detail)[0];
      const tempObj = {
        vip_type: 0,
        payment_status_list: 1
      }
      this.setData({ [`selectData[${tempObj[key]}].defaultValue`]: val.detail[key] });
      let params = {};
      for (const item of selectData) {
        if (item.defaultValue) {
          params[item.code] = item.code === 'payment_status_list' 
                            ? item.defaultValue.split('$') 
                            : item.defaultValue;
        }
      }
      this.setData({
          'bazaarParms.$filter': formatterParams(params, { payment_status_list: 'in' }, true),
      }, () => {
          app.showLoading();
          this.init();
      })
    },
    // 获取列表
    initBazaar(callback) {
        const that = this;
        let { bazaarlist, bazaarHasData, bazaarParms } = that.data;
        that.setData({
            bazaarIsNull: false,
            bazaarIsFlag: false
        });
        // 测试 
        mine.orderlist(bazaarParms).then(res => {
            // console.log(res)
            let { items: ary } = res
            ary.forEach(item => {
                item['create_time'] = formatDate(item['create_time'], 'yyyy年MM月dd日 hh时mm分')
                item['payment_method'] = payConstant[`${item['payment_method']}`]
                item['paymentStatus'] = statusConstant[`${item['payment_status_list']}`]
            });
            // let tempArr = [], tempObj = {
            //   create_time: '2002-12-22',
            //   payment_method: '支付宝',
            //   paymentStatus: '未支付',
            //   payment_status_list: 'UNPAID',
            //   subject: '1年个人VIP会员',
            //   pay_code: 1657186994494589,
            //   money: 360
            // };
            // for (let i=0; i<10; i++){
            //   tempArr.push(tempObj)
            // }
            // ary = tempArr;
            if (ary.length < bazaarParms.$limit) bazaarHasData = false;
            that.setData({
                bazaarlist: bazaarlist.concat(ary),
                bazaarHasData,
                bazaarIsFlag: true
            }, () => {
                if (!that.data.bazaarlist.length) that.setData({
                    bazaarIsNull: true
                });
            })
            callback && callback()
        }).catch(err => {
            callback && callback()
            app.showToast('获取数据失败!请稍后再试')
            console.log(err)
        })
    },
    // 下拉刷新
    bazaarRefresher() {
        const that = this;
        const { bazaarParms } = that.data
        app.showLoading('加载中');
        wx.showNavigationBarLoading();
        that.setData({
            'bazaarParms.$offset': 0,
            'bazaarParms.$limit': 10,
            'bazaarParms.$filter': bazaarParms['$filter'],
            bazaarlist: [],
            bazaarHasData: true,
        }, () => that.initBazaar(() => {
            that.setData({
                bazaarIsTriggered: false
            })
            wx.hideLoading();
            wx.hideNavigationBarLoading();
        }))
    },
    //加载更多
    bazaarloadMore() {
        let {
            bazaarParms,
            bazaarHasData,
            bazaarIsFlag
        } = this.data;
        if (!bazaarHasData || !bazaarIsFlag) return;//节流和没有数据
        bazaarParms.$offset += bazaarParms.$limit;
        this.setData({
            bazaarParms
        }, () => this.initBazaar());
    },
    // 点击去付款
    gopay(e) {
        const that = this
        const { item: { service_id, prepay_id, id } } = e.currentTarget.dataset
        getOderId(that, app, service_id, { prepay_id, id })
    }
})