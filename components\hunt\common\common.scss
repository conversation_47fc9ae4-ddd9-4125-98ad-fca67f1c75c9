/* 筛选相关 */
.searWrap {
  display: flex;
  background: #F2F2F2;
  padding-top: 12rpx;
}

.searWrap-r {
  /* width: 27.773333%; */
  width: 208rpx;
}

.searWrap-r ::-webkit-scrollbar {
  display: inline-block;
  width: 2rpx !important;
  /* 这个高度只取决于内容高度 */
  height: 2rpx !important;
  color: #ffffff;
}

.searWrap-r ::-webkit-scrollbar-track {
  /* -webkit-box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.3); */
  /* box-shadow: inset 0 0 6rpx rgba(0, 0, 0, 0.1); */
  border-radius: 10rpx;
  background-color: #fff;
}

.searWrap-r ::-webkit-scrollbar-thumb {
  border-radius: 10rpx;
  -webkit-box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6rpx rgba(0, 0, 0, 0.1);
  /* 控制滚动条颜色 */
  /* background-color: #39b54a; */
}

.searWrap-l {
  /* width: 100%; */
  width: 542rpx;
}

.searWrap-l ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 左侧样式begin */
.searL {
  width: 100%;
  height: 100%;
}

.searL .active {
  position: relative;
  background: #fff;
  border-radius: 16rpx 0 0 16rpx;
  /* border: 1px solid red; */
}

.searL .active::after {
  position: absolute;
  content: "";
  top: -26rpx;
  right: 0;
  width: 26rpx;
  height: 26rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAMlJREFUSEvt1SEOAjEQheF/EwQSQQICsQKxAoFE4DjCHmEFEssBcBgk90AgkUiOwTEeGTKbEEICIe0IQpPafn3TTKcgaBVBDn/o60r/VukkdUMSSepFQf0oqIyCJlHQIgpqoqBtFHTKDlkPAdcIaAnss0L29QAXoMoNrYCdffnZIEmVp7FUeSBJQ+AMlO0AS57IkSMwfZySSSEv1wEYP4/iJJCkDmAPvwHub5IU8masgfWrFB+VznugvZ11t+0BMPJDZ8AcsDRv1w3mV0k0AXl2AAAAAABJRU5ErkJggg==");
  background-size: contain;
  background-repeat: no-repeat;
}

.searL .active::before {
  position: absolute;
  content: "";
  bottom: -26rpx;
  right: 0;
  width: 26rpx;
  height: 26rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAANlJREFUSEvt1jEKwkAQheE/IJhCMKVlCkHLFBbewCvYp7AVPIQWHsF7aGfhBew8hIcYGZiVCK6CZqcQA4GwDPPxZkmyGZFLRLpADnSAnt0DoATGwASYWk2szX09e1vxokBE+sAcWBoerf4KCl1FRJMvgE0sYStQAxwBBxvvQ7pWIe0sIrqPe6BqSq1DDewEDAOWBDJMx3gOe5YMMmwFbPU5NaTv4EVHmBSyVDWw84AK4JocslRHL2jtBdVe0MwLqryg0gsqfg7KXRIl/6gm//E9O6H8R/fxMfAGrshJOEGy/j4AAAAASUVORK5CYII=");
  background-size: contain;
  background-repeat: no-repeat;
}

.searL .tit {
  position: relative;
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  padding-left: 32rpx;
}

.searL .active .tit::after {
  position: absolute;
  bottom: 14rpx;
  /* bottom: 20rpx; */
  left: 52rpx;
  /* left: 50%; */
  /* transform: translateX(-50%); */
  content: "";
  width: 72rpx;
  height: 6rpx;
  background: linear-gradient(90deg, rgba(231, 36, 16, 0.99) 26%, rgba(231, 36, 16, 0.5) 98%);
  border-radius: 2rpx 2rpx 2rpx 2rpx;
  opacity: 0.7;
}

.searL .tit_cont {
  overflow: hidden;
  transition: all .3;
}

.searL .tit_cont view {
  height: 88rpx;
  padding-left: 32rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #525665;
  line-height: 88rpx;
}

.searL .tit_cont .child-active {
  position: relative;
  color: #E72410;
  font-weight: 600;
  font-size: 24rpx;
}

.searL .tit_cont .child-active::after {
  content: "";
  position: absolute;
  bottom: 14rpx;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 32rpx;
  background: linear-gradient(180deg, rgba(231, 36, 16, 0.99) 0%, #F17B6F 74%);
}

.searL .tit>view {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.searL .active .tit>view {
  font-weight: 600;
}



.searL .tit>image {
  width: 20rpx;
  height: 20rpx;
  margin-left: 12rpx;
}

/*  左侧样式 end*/

.footer {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
  background-color: #fff;
  /* border-top: 2rpx solid #f5f5f5; */
  display: flex;
  justify-content: space-between;
  padding: 10rpx 24rpx;
  box-shadow: 8px 0px 8px 2px rgba(204.00000303983688, 204.00000303983688, 204.00000303983688, 0.20000000298023224);
}

.footer text {
  width: 220rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  /* background: #E72410; */
  background: linear-gradient(90deg, #FFB2AA 0%, #E72410 100%);
  color: #fff;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  /* font-weight: 600; */
}

.footer text:nth-child(1) {
  color: #74798C;
  background: linear-gradient(315deg, #EEEEEE 0%, #F5F5F5 100%);
}

.footer text:nth-child(2) {
  color: #74798C;
  background: linear-gradient(315deg, #EEEEEE 0%, #F5F5F5 100%);
}

.company-filter {
  display: flex;
  flex-direction: column;
  position: relative;
  background: #FFFFFF;
}


.content {
  /* flex: 1; */
  width: 100%;
  background-color: #fff;
  padding: 0 32rpx 32rpx;
  /* margin-top: 12rpx; */
  overflow-y: auto;
  border-radius: 16rpx 16rpx 0px 0px;
}

.content .content-item-pop {
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 92rpx;
  align-items: center;
}


.content .content-item-pop .ipt {
  position: relative;
  flex: 1;
  margin-left: 28rpx;
}

.content .content-item-pop .ipt>input {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  /* color: #525665; */
  color: #E72410;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.placeCls {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.cnt-ipt {
  position: relative;
  margin-top: 40rpx;
}

.content .cnt-ipt::before {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.content .content-item-pop::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  bottom: 0;
  left: 0;
  transform: scaleY(0.5);
}

.content .content-item-pop-r {
  display: inline-flex;
  align-items: center;
  flex: 1;
}

.content .content-item-pop-r text {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  flex: 1;
  margin-left: 28rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.content .content-item-pop-r text.has {
  /* color: #525665; */
  color: #E72410;
}

.content .content-item-pop-r image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

.content .content-item-pop .title {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 400;
  color: #20263A;
  flex-shrink: 0;
}

.content .content-item {
  font-size: 25rpx;
  color: #5C6070;
  padding: 48rpx 0 0rpx;
  background-color: #fff;
}

.content-item .tit {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.content-item .tit-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
}

.content-item .tit-text view:nth-child(1) {
  width: 60rpx;
  height: 1rpx;
  background: rgba(155, 158, 172, 1);
  margin-right: 20rpx;
  transform: scaleY(0.5);
}

.content-item .tit-text view:nth-child(2) {
  width: 60rpx;
  height: 1rpx;
  background: rgba(155, 158, 172, 1);
  margin-left: 20rpx;
  transform: scaleY(0.5);
}

/* 企业规模说明--icon*/
.filterexp {
  display: inline-block;
  margin-left: 12rpx;
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAAXNSR0IArs4c6QAAANhQTFRFAAAAgICAn5+flZWqn5+vmZmzn5+1m5u2paW9n5+3oKi9nKO4o6O+n5+5o6O4o6i4oKW5oqe5oqa6n6O3oqW7oKO7oaW4oaW7oKa7oaS5oKW5oKW8oaS5oKW5oaO6oaa6oKS6oaW6oKS6oaW7oKW5oKS5oKa5oKS6oKW6oKW6oKS6oKW6oKS6oKW6oKS5oKW5oKS5oKW5oKS5oKS6oKW6oKW6oKS6oKW5oKW6oKS5oKS6oKW5oKW6oKS5oKW5oKW5oKS5oKS6oKW6oKW6oKS6oKW6oKW6oKW66g9uAAAAAEd0Uk5TAAQIDBAUGBwfICMkJygvLzM3P0BHS09PU1dbW19jZ2drb3N/i4+Pl5ebn5+jp6uvs7O3x8fLz8/P09PX19vf4+fr6/P39/vWY4YUAAABYklEQVQ4y32T2VrCMBBGxxIXrLivFVxBhSgoWhCLMWDsef838qaFtrbO1SwnmfmSf0QWpgI9nsM87AZK/tqm/mFh7n6rUPbaMRBHL1oPJgCu4+WOT4HX1mrSqzkGoo1lfceCOcyeODZgd9Now0K/Vuj5BHYz8afQ+Tt1ByIv9QZpdu/6ej/1+9AWEdlymMXEt5nLaobYF5F7OJYyQA5BiyjHeNn4YjS6WUZvOCUBtKTCWhBIF9aqgNWYnoSYTKp+cOBnwiljmTPMZG7zT/LCXEBXAxoEHv8H/m0xwMooN2QB+CAUDetVgIKunMNlFdCEM1GOjyrgHadEenBaDpzAg4j4MV+qDPAMriEi0obnlSS53WwulPiUwl5UKrk7mCRC8i08F0Rb64Otp9GuBXOSrR8ZsI3ML0fA+1UijLWLEJjUc2vQcQBmqB+HnwBx2yvM1Hhwy+X97vklAlNBN5zBLL/+v69tTgQXnGC4AAAAAElFTkSuQmCC");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.vip {
  width: 64rpx;
  height: 32rpx;
  margin-left: 8rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAiCAYAAADvVd+PAAAAAXNSR0IArs4c6QAADd5JREFUaEPVWWmQVNUVPue+7tl7FpZhAGEYEBEIEVlEEq2AlbI0hkhMjGXQCIpgVRbLaFwqYlAxMSKktKImhQga/WFiiZYaQUWx4lKRRXADAjLCDAzDDLPv3f1O6pxz71tmQLEqf9Ildk+/+27f853vfGd5CKf4qt4wvzSR8i9Bn74LgFMB6DREGMy3EyHy/wEQEYGA/wteyCsAgN+Rl8i7LCRENEjA74jkroX7xNf3vz/YE7AREb8AwF1o4J32nr4Xq+aubzkV0/g0X/qq3zx/XCabvZMQFyBAgo2Rg1tD1Xh9iU3EdvTfFtliQuCb1Fhd5Qzkew3pn4ahZGQsihYkC6D+tgAogIV72L3kuukmgGdyE/6DxTMf2/tlBp4UgOq35uTlZFL3EuCNCJTg07nF9vTi6RAM4YL1tPBBvSv32cM570eYEBghCJrgfmGBQbIGh8a6ey17HJDut5SRRnEk7AVj/tLR2XpH1dz1PScC4oQAiNf97EsAeKZuqIx1n9mRciMJe8XumNPZEL4gdrMRRhlvjRLqyvUAHDZHgPIFt6h3NcQC1vC1GAh8FgM+IRovYJnyxQibAMDsNQm8qGzaqoP9QRgAwJFN86eTyb5MhBWB0c6zupsjfACM2q9e5sPrKjaaD0tAweeQpnKHoka6VqIDwHovjHf+ZCzQoVZI6KHcaf1jmEDKON4n2MsCBnAUjXdx2bQ/7oqCEAPgyObvVfrZxPsAMDwa175Ep2W4IzofWGVPtQiMeM5yVeLdfs+WhQGExgogoLFxf1JxFCvYAImPQEQDARWAVXyY80485bOGGzIqyig541FMJs4vnbLi89CN9lP1ujl5OaeldhDhRBUzPac1XjxZcd7jmMgvHxBKLXvWQOfhTVYEEU0kdlNjr8Siyktj92R6GqBx6+0ElIXCUZdAqurywBF+pguOb72F/Gya/Qv55edS8RmLvlKss30tkG7bDz1H3ya/p87iYkFRLRIhJsK9ra05Z1fNXS6aEGxcu2nen9DgjWy4BVWoTByUEr8AFeetOTEAe9dBZ+0/CQ0vtHQGhEThKBg6YwWiyYkB0PzZI9R9bKv8zpBpyyCneFxwvav+PWjft16JhAglZyyG3CEzvhIAtwH5aWjft47SbfsRPSQTMCOSZglXlZ59728CAOrenDc5m4FtiJirG0XVnJmu9EtV/QRKTv/pgMO07HuKOg+9YpXSxikgDJ76W8wtmxQzvvvYVmj67BHxSDJ/GJXPuj+2X9MnD0Nfy6csaYAmCUPPWYno2WMN4N6Jv8j2NELrp6uZtZIyjTGalGxWAjAZMt7E0inLPpcfP/zavDUEuDia2+WzEF/USQDwAaFo1Pdx0JmL4h7d+xR0HX5VCxr7I3nls3HQ5J/H1rF36v99G2V7mxXQynlYXPXDYI2f7oBjH9wquZVjN1k0GlJjLmOsuL44AQsQTKIQkoWBZAV7te5+hLLdhyUTsDQHYWmMehRxfck371qE1RvmlCYLU3UAmGcTjt0kFB2fjJRzPjGtR8OIb62KHabtwD+otfoFm75Ya/Kx4tyV4OWWxoH6zzPQeXgzH0o0pmLW7yGqKV3171Hb/qclHQh1JfWyEKkSaip0YanqzumvcNgMKh0fZ2ZH9XPQ1/whGeOx2gKiF9Qjqm+YSRUkhmDtGz+4BnxY7+I+KFVtWuN0w4D5zAYy4OUNhZHn/zlmWOuB56Gt+nlwKJdOWAhFIy+IrelrPwT121fYnRCTRaNg2Iy7YmsaP1pN6fYDrNphKpOAlLpBKMkaE7nJ1gQGKs5ZEQuVji82QF/zDtUAhtMYCauw8JIy5GoU+iPTX73iihCXmrTUlfQGvo9okkU08jtr4gyo3kBt1RvklmThGCyf+TtBPBAmykL9thWQ6ayRgof3LB57GRSPvihYw2Fx9INl4Ilw8fbiNr2uxZZUha7yRjmTdiBe7iAon3ZH7Eytex+nbNchMCahxTdTXz9pGa30ehIPv37pVkSczsWK/lakCrMsYMOFiRyaXiH0B6C1+gVqrd4gib9i5j2Qkxod82zH4behZd/TWu1ptQLDzrkPE3nSS8mro3YztB98kYzYrQConrhKUMNBaS9+1IILE1A6/meYWzYhBDzbC00fPUCsWsYzIGFg6wIGQpHiFAm78Mjmy45xNoo0J3LQSLmp3R4CZX1Gx6NRFzwZQ7v90EZq2f+s0L5swtWxa5neZqj/4E4CythCBYnT3pCzbomta9i1GrKdB8kYpqmHOamxgVawCPIhVLtsf2ESYHKHYf6wWRAFkq3qqn8fug9vJK6NPc8THXCFUbT4IoDjeGTzjzjT20ot7NaiIDADlG7ILMDT5j4Ro3jbodeg7dCrNHzmcvRyS2Leb9q9lrobttqYZkUGSFVeikWjLgzWZXqaoGH73SSKjQZzS86A0olLY/uc6h+cSZo+ewwMdYvhDChaBmi1GO04kfDIm5f7gbFaegZhoLBxCpJY4KpQBGn4eY+iSRQEZ2qreR3476Lh346ds6thBzTvWWNj2sU1UPn05ej1o39HzSuqNmikMswvn3WqNkeo38ehBn53rRrP9A9E0LO9hhhoMwoiHnnrigZEHKzN7kAGWAWWgPMtCypmP4gsPO7V13aAcorHxigtOX/b3UR9zQH9mL7J1DgcPOXGmHHHP36Y+toPslAJs4ZOWwYmWfi1AOhr/wI6Dr5ElG7WmGcAWP09q/7SiYoQCgmA+xCi41i35codCDjVDSpEfa3yarxpf800IMuC8pkrMFkwsPiInrjl8+eg88gWTo1Sick+YDBVOR8KR3wnQv9GaNhxP0griwjJorFQNumGOJjZPiBi+CMv8iGbboVMRw31tuyGTMdBbYgQ0fMSOhbh+I80SmK0UlwHMEDbsX7LgnVg8BqnsP3FMGgEmQGci4nr97swp2jUST2U7qiFxo9WcbPDxwBuy1l1fTBYPn05mGRRcC+rf2ftRuJGglNw0eh5WFARD6XjnzxK6c5aZ2Bk4uRJpmD2aqzrP6a9TBUYeM2BYnAk1HUPorVY//ZVCwDN30IGhLW8zdk2/yBluRYGoEFTfo25pWHa6Y9Ew4cPUKar1h7K9vg8VkqNx7JJN8SWN+xcCX5vo1YgADT4rNtjQsr1QcOuVZLDudCSis7WB+px5qyr9hQA+UaMl9Zbx2sus2l6kwwDBIuwaduSknRXuhENcK6wg73okJJN1nGQzQZUNukXmDdo8gkZ0Fn3LrQd+LukM/6n7YFEEBRVXYH5Q2cE96U7j0DTJw9rcQSIOakxVDZxSYz+XUffpfaajVLQyJDH88Tj7h6t8PiSsEFYYoynlbPxgklTDABhA/R0t3aOkFuPvXPtOgBcGB1MKEoysZWv2QCpAwigbOISyBt81gAAsn1tcGz7PVKAePYwwQ9jAgZNvRNNIj+kf80m6qzbIpRlAFKjL6H8YbNjADTveYIyncwmEWkBwFV0mtNV3JTmQaFlhy3h8CWMe1do0dq8sddfLz/W8u6ScWmg3YDIw08dZbnJr63cGAifCHwfsPj0q6Bg2LkDAGja+xT0Ht8pnueS1o3FWEeSqfFYMuHa2D2Nux4kv7dFRInpXTblZvRywjqChyNNH68WD7ucrqHg2lsVVw0FNVY1zk6E3HRZKOMqQGFzbzqdPbt4wtI9AdoN7y1dCYg3W3Sl7g5pI6EvHSmDkKq6HAoqzo95qqfpU2jesxa08tJDuAPxKQsrf4x5Q6aF9O+ogZY9f9UZnjGQKBiNpWdeFwOo5/gu6Kx5OSxoOJhsWnNp2wa5MEB6GRU+/RyPfW0mtKp8KG/c9TdpPrSv6rcW5qXyC2QkJl2dfB8xQryPKABUzqeCEXOCeznnN+58gCjdIt6Xn3Ze4AkyJqHkG7fG6V/7OnTXv8P0lwMXjLwQ88vjrGo/8Cxl2quloOHxDse4MZLi1PH9xI1j3ppjnyvEfKQAGNxdB7XTqqr6jcQkFLb9clwmi//SoahWS5outALkTOxzLuR6wGYEpxvic89w+ynv2sFpD+HCRwotgTagqXsAYud1mmUCWtuYD0taSWdxqutiHf4EE5+44a72AaKjBDA7f+ziYDzebyVAy/ZfTc9mzUvc2Mk42mpCMByVekBF0Y2mGSjJwzb/arpinPj5gK6XLk6G98GYWsXLzfgFHPWfDkSkK2VUgVMav7v4t029LWZc3CvtLTXkOZ2luJ0cw1FDeHHO2OtOPhZ34dC84+ZKP0sbEXGCayBsMxR0hvoLsUdbqsKSh13xEU5w3ETHFVzhc4Gw/xAD7CM03Zrzu/O4kc5Ozhg0NdJfCMW4PRbwbJwH76rmu8FLzMsbvTAYhztbBzAgqgnFxeV/QMCliDwuUzwtEDYxstoxwZ3ohWNo1/cHMqMjyqDrtEbY+jzs+W1+l4ch9gFHMMxQveDvw6dFwSxeiaPUDF/cgz9Uh7XLXMzHVDYqgv0vuL/bd942OeObm4jMAmMg1/HJUZUNDNRWDLRPhGxKsk/NwsdZ7gGHrexsj2+PIlkGuZCJGKmFTWC4FT8JlRN4XhUhTQTPIMF9eacv3n8y22yIfNnl8Frzh8tLAfrmo0lyoT4VCMYgmsHB2Eo5K6JmHyREu69wwOIeXVkgonOHaOqypVyg9pYNMpZyTZo7HXFXh1CLiDvBxze6TNsrZVU3/W8ej58EHp3S2ov2c4x7/e+zMRl87Zgae+jqBN3uZK8FmeHUXPX1Vp1UA77eNv+/q/8L/vnjPLokfSEAAAAASUVORK5CYII=') no-repeat;
  background-size: 100% 100%;
}

.expPop {
  /* 企业规模说明弹窗 */
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  padding: 48rpx 32rpx 60rpx;
}


.content-item .wrap {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  transition: all 1s ease;
}

.content-item .wrap>text {
  padding: 0 26rpx;
  height: 56rpx;
  line-height: 56rpx;
  background-color: #F4F4F4;
  /* border: 2rpx solid #F4F4F4; */
  margin-right: 20rpx;
  margin-top: 24rpx;
  border-radius: 4rpx;
  font-weight: 400;
}

.content-item .wrap text.active {
  /* border: 2rpx solid #E72410; */
  color: #FFFFFF;
  background: linear-gradient(90deg, #FFB2AA 0%, #E72410 100%);
}

.content-item .wrap .input-wrap {
  display: flex;
  align-items: center;
  margin-top: 25rpx;
}

.content-item .wrap .input-wrap>view {
  display: flex;
  width: 350rpx;
  height: 56rpx;
  padding: 0 19rpx;
  background-color: #F4F4F4;
  border-radius: 4rpx;
  margin-right: 19rpx;
  color: #9B9EAC;

}

.content-item .wrap .input-wrap>view .year {
  flex-shrink: 0;
  line-height: 56rpx;
}

.content-item .wrap .input-wrap>view input {
  height: 100%;
}

.content-item .wrap .input-wrap>view .short-line {
  width: 200rpx;
  height: 100%;
  line-height: 52rpx;
  text-align: center;
}

.content-item .wrap .input-wrap>view.active {
  /* border: 2rpx solid #E72410; */
  color: #E72410 !important;
  background-color: rgba(231, 36, 16, 0.1);
}

.content-item .zhankai {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 28rpx;
}

.content-item .zhankai>image {
  width: 24rpx;
  height: 24rpx;
  padding-left: 30rpx;
}

/* 保存选项弹窗 */
.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}

@-webkit-keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.mask {
  position: fixed;
  z-index: 99;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  /* display: flex;
    justify-content: center;
    align-items: center; */
}

.mask .box {
  width: 604rpx;
  height: 354rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  opacity: 1;
  margin: 434rpx auto 0;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.mask .box .tit {
  padding: 48rpx 0 24rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A
}


.mask .box .inpt {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 524rpx;
  height: 88rpx;
  background: #F7F7F7;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  padding: 24rpx 28rpx;
}

.mask .box .inpt>input {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #5B5F6F;
}

.placeholder-input {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.mask .box .btn {
  position: relative;
  display: flex;
  height: 100rpx;
  width: 100%;
  margin-top: 48rpx;
}

.mask .box .btn::before {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.mask .box .btn::after {
  content: " ";
  width: 1px;
  height: 100rpx;
  background: #eee;
  position: absolute;
  top: 0;
  left: 50%;
  transform: scaleX(0.5) translateX(-50%);
}

.mask .box .btn view {
  flex: 1;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 100rpx;
  text-align: center;
}

/* input下面搜索框样式 */
.child-box {
  position: absolute;
  width: 100%;
  top: 106rpx;
  background: #fff;
  z-index: 15;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(32, 38, 58, 0.20);
}

.child-box-ul {
  height: 480rpx;
}

.search-li {
  height: 96rpx;
  line-height: 96rpx;
  margin: 0 28rpx;
  border-bottom: 1px solid #f7f7f7;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 670rpx;
}

.search-li .listtext {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
}


.search-li .searchHigh {
  color: #E72410 !important;
}

.child-box .loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading .size {
  width: 80rpx;
  height: 80rpx;
}

/* loading 样式 */
.weui-loading {
  margin: 0 5px;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
  -webkit-animation: weuiLoading 1s steps(12, end) infinite;
  animation: weuiLoading 1s steps(12, end) infinite;
  background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;
  background-size: 100%;
}



.loading .text {
  padding-top: 20rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #9b9eac;
}

.hover {
  background-color: rgba(116, 121, 140, 0.1) !important;
}

/* 产业优选特殊样式23.2.23 */
.yxbg {
  position: relative;
  width: 100% !important;
  margin-top: 48rpx;

}

.yxbg .tit-text {
  position: absolute;
  top: 16rpx;
  width: 100%;
  z-index: 10;
  height: 100%;
  font-size: 32rpx;
  font-weight: 600;
  color: #20263A;
}

.yxbg .yxbgImg {
  position: absolute;
  left: -24rpx;
  right: -24rpx;
  top: 0;
  /* border: 1px solid blue; */
  background: #F9F2E2;
  width: 526rpx;
  height: 120rpx;
  border-top-right-radius: 8rpx;
  border-top-left-radius: 8rpx;
  overflow: hidden;

}

.yxbgImg image {
  width: 100%;
  height: 100%;
}

.yxbglt {
  /* border: 1px solid red; */
  position: relative;
  z-index: 1;
  margin-top: 36rpx;
  width: 526rpx;
  transform: translateX(-24rpx);
  border-left: 8rpx solid #F9F2E2;
  border-right: 8rpx solid #F9F2E2;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  padding: 36rpx 16rpx 20rpx !important;
}

.yxbglt1 {
  width: 526rpx;
  transform: translateX(-24rpx);
  border-left: 8rpx solid #F9F2E2;
  border-right: 8rpx solid #F9F2E2;
  padding: 20rpx 16rpx 20rpx !important;
}

.yxbgend {
  width: 526rpx;
  transform: translateX(-24rpx);
  border-left: 8rpx solid #F9F2E2;
  border-right: 8rpx solid #F9F2E2;
  border-bottom: 8rpx solid #F9F2E2;
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  padding: 20rpx 16rpx 20rpx !important;
}