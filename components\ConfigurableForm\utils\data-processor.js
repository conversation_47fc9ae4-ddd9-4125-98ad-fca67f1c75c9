/**
 * 数据处理工具
 * 专门处理多选数据和参数转换
 */

const {
  clone
} = require('../../../utils/util');
const {
  POPUP_FIELD_MAPPING
} = require('../config/fields');

/**
 * 处理多选数据 - 有父级时，子级不需要的情况
 * 优化逻辑：提高性能，增加错误处理，支持配置化
 * @param {Array} ary - 原始数据数组
 * @param {Object} options - 配置选项
 * @returns {Array} 处理后的数组
 */
function handleMultiple(ary = [], options = {}) {
  // 参数验证
  if (!Array.isArray(ary) || ary.length === 0) {
    return [];
  }

  const {
    statusField = 'status',
      checkedValue = 'checked',
      isChildrenField = 'ischildren',
      codeField = 'code',
      parentField = 'parent'
  } = options;

  try {
    // 深度克隆避免修改原数组
    let tempArray = clone(ary);

    // 筛选出已选中且不是子节点的项
    let checkedArray = tempArray.filter(item =>
      item[statusField] === checkedValue && !item[isChildrenField]
    );

    // 如果没有选中项，返回原数组
    if (checkedArray.length === 0) {
      return ary;
    }

    // 创建已选中项的code映射，提高查找性能
    const checkedCodes = new Set(checkedArray.map(item => item[codeField]));

    // 过滤掉父级已被选中的子级项
    checkedArray = checkedArray.filter(item =>
      !checkedCodes.has(item[parentField])
    );

    // 如果过滤后没有有效选中项，返回原数组
    if (checkedArray.length === 0) {
      return ary;
    }

    // 创建需要移除的父级code集合
    const parentCodesToRemove = new Set(checkedArray.map(item => item[codeField]));

    // 移除选中父级的所有子级
    tempArray = tempArray.filter(item =>
      !parentCodesToRemove.has(item[parentField])
    );

    return tempArray;

  } catch (error) {
    console.error('handleMultiple 处理失败:', error);
    return ary; // 出错时返回原数组
  }
}

/**
 * 处理搜索参数数据 - 转换为查询格式
 * 优化逻辑：使用配置化映射，支持动态字段处理
 * @param {Object} params - 原始参数对象
 * @param {Object} options - 配置选项
 * @returns {string} JSON字符串格式的处理结果
 */
function handleData(params, options = {}) {
  if (!params || typeof params !== 'object') {
    console.warn('handleData: 参数必须是对象');
    return JSON.stringify({});
  }

  const {
    returnString = true,
      processMultiple = true,
      fieldMapping = null
  } = options;

  try {
    // 深度克隆避免修改原对象
    let result = clone(params);

    // 使用配置化的字段映射
    const mapping = fieldMapping || getDefaultFieldMapping();

    // 处理弹窗多选字段
    Object.keys(mapping).forEach(resultKey => {
      const config = mapping[resultKey];
      const sourceData = params[config.sourceField];

      if (sourceData && Array.isArray(sourceData) && sourceData.length > 0) {
        if (processMultiple && config.needMultipleHandle) {
          // 需要处理多选逻辑的字段
          const processedData = handleMultiple(sourceData);
          result[resultKey] = processedData
            .filter(item => item.status === 'checked')
            .map(item => item.code);
        } else {
          // 直接提取选中项的code
          result[resultKey] = sourceData
            .filter(item => item.status === 'checked')
            .map(item => item.code);
        }
      } else {
        result[resultKey] = [];
      }
    });

    // 清理源字段（可选）
    Object.values(mapping).forEach(config => {
      if (config.removeSource && result[config.sourceField]) {
        delete result[config.sourceField];
      }
    });

    // 处理特殊字段
    result = processSpecialFields(result);

    return returnString ? JSON.stringify(result) : result;

  } catch (error) {
    console.error('handleData 处理失败:', error);
    return returnString ? JSON.stringify(params) : params;
  }
}

/**
 * 处理特殊字段
 * @param {Object} data - 数据对象
 * @returns {Object} 处理后的数据
 */
function processSpecialFields(data) {
  // 处理产业链字段（比较特殊的逻辑）
  if (data.industrial_list_data && Array.isArray(data.industrial_list_data)) {
    data.industrial_list = data.industrial_list_data
      .filter(item => item.active === true)
      .map(item => item.code);
  }

  // 处理布尔值字段
  const boolFields = [
    'fixed_phone_flag',
    'mobile_phone_flag',
    'email_flag',
    'tax_credit_flag',
    'trade_mark_info_flag'
  ];

  boolFields.forEach(field => {
    if (data[field] && Array.isArray(data[field]) && data[field].length > 0) {
      try {
        data[field] = JSON.parse(data[field][0]);
      } catch (e) {
        data[field] = data[field][0];
      }
    }
  });

  return data;
}

/**
 * 处理回填数据 - 用于数据回显，不做多选处理
 * @param {Object} params - 参数对象
 * @returns {Object} 处理后的数据
 */
function handleBackfillData(params) {
  if (!params || typeof params !== 'object') {
    return {};
  }

  // 回填数据不需要做多选处理，保持原始结构
  return handleData(params, {
    returnString: false,
    processMultiple: false
  });
}
module.exports = {
  handleMultiple,
  handleData,
  handleBackfillData,
  processSpecialFields
};