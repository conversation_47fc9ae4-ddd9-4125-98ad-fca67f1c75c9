/**
 * 通用工具方法库
 * 提取可复用的工具方法，减少代码重复
 */

const {
  clone
} = require('../../../utils/util');
const {
  SEARCH_CONSTANTS,
  RANGE_INPUT_TIPS
} = require('../config/fields');

/**
 * 数据处理工具
 */
const dataHelpers = {
  /**
   * 处理搜索参数数据
   * @param {Object} params - 参数对象
   * @returns {Object} 处理后的参数
   */
  handleData(params) {
    let result = clone(params);
    // 处理布尔值字段
    SEARCH_CONSTANTS.RADIO_FIELDS.forEach(field => {
      const temp = result?.[field]?.[0] || '';
      if (temp && (temp === 'true' || temp === 'false')) {
        result[field] = JSON.parse(temp);
      } else {
        delete result[field];
      }
    });
    return result;
  },

  /**
   * 转换参数结构
   * @param {Object} data - 原始数据
   * @returns {Object} 转换后的数据
   */
  handlestructure(data) {
    return this.handleData(data);
  },

  /**
   * 转换参数格式
   * @param {Object} params - 参数对象
   * @returns {Object} 转换后的参数
   */
  transformParams(params) {
    const result = {};

    Object.keys(params).forEach(key => {
      if (Array.isArray(params[key]) && params[key].length > 0) {
        result[key] = params[key];
      } else if (typeof params[key] === 'string' && params[key].trim() !== '') {
        result[key] = params[key];
      }
    });

    return result;
  },

  /**
   * 深度克隆对象
   * @param {any} obj - 要克隆的对象
   * @returns {any} 克隆后的对象
   */
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  }
};

/**
 * 验证工具
 */
const validationHelpers = {
  /**
   * 验证注册资本范围 - 最低资本不能高于最高资本
   * @param {string|number} minCapital - 最低资本
   * @param {string|number} maxCapital - 最高资本
   * @returns {Object} 验证结果
   */
  validateCapitalRange(minCapital, maxCapital) {
    const result = {
      valid: true,
      message: ''
    };

    // 如果都为空，验证通过
    if (!minCapital && !maxCapital) {
      return result;
    }

    // 转换为数字进行比较
    const minValue = parseFloat(minCapital);
    const maxValue = parseFloat(maxCapital);

    // 检查是否为有效数字
    if (minCapital && isNaN(minValue)) {
      result.valid = false;
      result.message = '最低注册资本必须是有效数字';
      return result;
    }

    if (maxCapital && isNaN(maxValue)) {
      result.valid = false;
      result.message = '最高注册资本必须是有效数字';
      return result;
    }

    // 检查数值范围
    if (minCapital && minValue < 0) {
      result.valid = false;
      result.message = '最低注册资本不能小于0';
      return result;
    }

    if (maxCapital && maxValue < 0) {
      result.valid = false;
      result.message = '最高注册资本不能小于0';
      return result;
    }

    // 核心验证：最低不能高于最高
    if (minCapital && maxCapital && minValue > maxValue) {
      result.valid = false;
      result.message = '最低注册资本不能高于最高注册资本';
      return result;
    }

    return result;
  },

  /**
   * 验证注册年限范围 - 最低年限不能高于最高年限
   * @param {string} minDate - 最低年限（日期字符串）
   * @param {string} maxDate - 最高年限（日期字符串）
   * @returns {Object} 验证结果
   */
  validateDateRange(maxDate, minDate) {
    const result = {
      valid: true,
      message: ''
    };

    // 其中一个为空 验证通过
    if (!minDate || !maxDate) {
      return result;
    }

    try {
      let minTime, maxTime;

      // 转换为时间戳进行比较
      if (minDate) {
        minTime = new Date(minDate).getTime();
        if (isNaN(minTime)) {
          result.valid = false;
          result.message = '最低年限日期格式不正确';
          return result;
        }
      }

      if (maxDate) {
        maxTime = new Date(maxDate).getTime();
        if (isNaN(maxTime)) {
          result.valid = false;
          result.message = '最高年限日期格式不正确';
          return result;
        }
      }

      // 核心验证：最低年限不能高于最高年限
      // 注意：在注册时间中，较早的时间表示较高的年限
      if (minTime < maxTime) {
        result.valid = false;
        result.message = '最低年限不能高于最高年限';
        return result;
      }

      return result;
    } catch (error) {
      result.valid = false;
      result.message = '日期验证失败，请检查日期格式';
      return result;
    }
  },

  /**
   * 验证从业人数范围 - 最低人数不能高于最高人数
   * @param {string|number} minPerson - 最低人数
   * @param {string|number} maxPerson - 最高人数
   * @returns {Object} 验证结果
   */
  validatePersonRange(minPerson, maxPerson) {
    const result = {
      valid: true,
      message: ''
    };

    // 如果都为空，验证通过
    if (!minPerson || !maxPerson) {
      return result;
    }

    // 转换为数字进行比较
    const minValue = parseInt(minPerson);
    const maxValue = parseInt(maxPerson);

    // 检查是否为有效数字
    if (minPerson && isNaN(minValue)) {
      result.valid = false;
      result.message = '最低从业人数必须是有效数字';
      return result;
    }

    if (maxPerson && isNaN(maxValue)) {
      result.valid = false;
      result.message = '最高从业人数必须是有效数字';
      return result;
    }

    // 检查数值范围
    if (minPerson && minValue < 0) {
      result.valid = false;
      result.message = '最低从业人数不能小于0';
      return result;
    }

    if (maxPerson && maxValue < 0) {
      result.valid = false;
      result.message = '最高从业人数不能小于0';
      return result;
    }

    // 核心验证：最低不能高于最高
    if (minPerson && maxPerson && minValue > maxValue) {
      result.valid = false;
      result.message = '最低从业人数不能高于最高从业人数';
      return result;
    }

    return result;
  },

  /**
   * 通用范围验证方法
   * @param {string|number} minValue - 最小值
   * @param {string|number} maxValue - 最大值
   * @param {Object} options - 配置选项
   * @returns {Object} 验证结果
   */
  validateRange(minValue, maxValue, options = {}) {
    const {
      fieldName = '数值',
        minFieldName = '最小值',
        maxFieldName = '最大值',
        allowNegative = false,
        parseType = 'float' // 'float' 或 'int'
    } = options;

    const result = {
      valid: true,
      message: ''
    };

    // 如果都为空，验证通过
    if (!minValue || !maxValue) {
      return result;
    }

    // 根据类型转换数值
    const parseFunc = parseType === 'int' ? parseInt : parseFloat;
    const minNum = parseFunc(minValue);
    const maxNum = parseFunc(maxValue);

    // 检查是否为有效数字
    if (minValue && isNaN(minNum)) {
      result.valid = false;
      result.message = `${minFieldName}必须是有效数字`;
      return result;
    }

    if (maxValue && isNaN(maxNum)) {
      result.valid = false;
      result.message = `${maxFieldName}必须是有效数字`;
      return result;
    }

    // 检查负数
    if (!allowNegative) {
      if (minValue && minNum < 0) {
        result.valid = false;
        result.message = `${minFieldName}不能小于0`;
        return result;
      }

      if (maxValue && maxNum < 0) {
        result.valid = false;
        result.message = `${maxFieldName}不能小于0`;
        return result;
      }
    }

    // 核心验证：最小值不能大于最大值
    if (minValue && maxValue && minNum > maxNum) {
      result.valid = false;
      result.message = `${minFieldName}不能高于${maxFieldName}`;
      return result;
    }

    return result;
  }
};

/**
 * 格式化工具
 */
const formatHelpers = {
  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @param {string} format - 格式
   * @returns {string} 格式化后的日期
   */
  formatDate(date, format) {
    format = format || 'YYYY-MM-DD';
    if (!date) return '';

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return format.replace('YYYY', year).replace('MM', month).replace('DD', day);
  },

  /**
   * 格式化货币
   * @param {number} amount - 金额
   * @param {string} unit - 单位
   * @returns {string} 格式化后的货币
   */
  formatCurrency(amount, unit) {
    unit = unit || '万元';
    if (amount === undefined || amount === null || amount === '') return '';

    const num = parseFloat(amount);
    if (isNaN(num)) return amount;

    return `${num.toLocaleString()}${unit}`;
  },

  /**
   * 格式化选项列表
   * @param {Array} options - 选项数组
   * @returns {string} 格式化后的字符串
   */
  formatOptions(options) {
    if (!Array.isArray(options) || options.length === 0) return '';

    return options
      .map(option => option.name || option.label || option)
      .join('、');
  },

  /**
   * 分割字符串为对象
   * @param {string} str - 字符串
   * @param {string} separator - 分隔符
   * @returns {Object} 分割后的对象
   */
  splitStringToObject(str, separator) {
    separator = separator || '$';
    const obj = {
      start: '',
      end: ''
    };

    if (str && str.indexOf(separator) > -1) {
      const arr = str.split(separator);
      obj.start = arr[0] || '';
      obj.end = arr[1] || '';
    }

    return obj;
  },

  /**
   * 对象转换为字符串
   * @param {Object} obj - 对象
   * @param {string} separator - 分隔符
   * @returns {string} 转换后的字符串
   */
  objectToString(obj, separator) {
    separator = separator || '$';
    if (!obj || typeof obj !== 'object') return '';

    const start = obj.start || '';
    const end = obj.end || '';

    return `${start}${separator}${end}`;
  }
};

/**
 * 通用工具函数
 */
const utils = {
  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 防抖后的函数
   */
  debounce(func, delay) {
    delay = delay || 300;
    let timeoutId;
    return function () {
      const args = Array.prototype.slice.call(arguments);
      const self = this;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(function () {
        func.apply(self, args);
      }, delay);
    };
  },

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 节流后的函数
   */
  throttle(func, delay) {
    delay = delay || 300;
    let lastTime = 0;
    return function () {
      const args = Array.prototype.slice.call(arguments);
      const self = this;
      const now = Date.now();
      if (now - lastTime >= delay) {
        lastTime = now;
        func.apply(self, args);
      }
    };
  },

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  /**
   * 检查是否为空值
   * @param {any} value - 值
   * @returns {boolean} 是否为空
   */
  isEmpty(value) {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }
};

// 导出模块
module.exports = {
  dataHelpers: dataHelpers,
  validationHelpers: validationHelpers,
  formatHelpers: formatHelpers,
  utils: utils
};