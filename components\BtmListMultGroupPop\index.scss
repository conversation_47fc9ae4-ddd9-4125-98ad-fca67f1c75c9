/* components/BtmListMultGroupPop/index.scss */

.btm-list-wrap {
  background-color: #fff;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #f0f0f0;
}

.header {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;

  .title {
    font-size: 32rpx;
    font-weight: 600;
    color: #20263a;
    margin-bottom: 8rpx;
  }

  .selected-count {
    font-size: 24rpx;
    color: #999;
  }
}

.list-container {
  flex: 1;
  height: 600rpx;
  max-height: 600rpx;
}

.list-item {
  padding: 0 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f8f8;
  }

  &.active {
    background-color: rgba(231, 36, 16, 0.05);

    .item-name {
      color: #e72410;
      font-weight: 500;
    }

    .item-checkbox {
      border-color: #e72410;
      background-color: #e72410;
      color: #fff;
    }
  }
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  min-height: 88rpx;
}

.item-name {
  flex: 1;
  font-size: 28rpx;
  color: #20263a;
  line-height: 40rpx;
  margin-right: 24rpx;
  word-break: break-all;

  // 文本溢出处理
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;

  &.checked {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABuZJREFUWEddV0uIXFUQrbqv2yTO68/MkIWJiZpuDUQTv3vBpbhzK7oTMZqdwSwkmRFBkzEfEEwiQpKJYjASZ9RFUERBEQQ/4MpVCML0JGKSnu4xRqdvyamqe7tnZvW6575bVafOOVXNRESddrmRhJ9kCvcJ8TpmClEoMFFBLIHsOUSmgon1szAVgSSIUCCiQEyFPjPbeyL6Pe4SwT2Mz4VEucWBfpYBv7vp0o3LrMGp8lJg3hBjDMJSkAQNiOCMly1AEGF9xqVkwQt8T8IFk7CQFMQhkIifQ7IShO09JE56hoIQ3bq1ws9xp1V/loh3ajAKdqnEQMQaKDIuTy9TIXZIqwMKJIzLCk3KAuTEArM+C97XxPW9Qs9Z4t/zYqvxhoisx2W4O4oUyESDahC2hDgEVMhoh1VggUgQxyBnR05iEGKr1BFk8uBIChATkpMVXmzXD+MzqjC40FsPDFgBuyYwgkSgwNqOyOLB8T4yUX4od4aQMxOnRPR+Q01R4MVW85hCiUqcUNZbrUjhS1ASxUxIsWD63hqyGRmHkNu5VLkmCHSsVdzZVn8nwYWXFH6RMNCKlFC5IqCBxgENMaIxJx5oEVa1ccJ5gmC4V7nBqhZN2nhQ8OK948cpRjay4R+shIqGiBFGIffvhv0umKLJcITlhpgT0XlElO8EoRn3mjKEeaHdfM8CI2OgjSrEmOsSs0uHpNPeZRKa9kclhiLsjBbk/Ya8vWoyTiAJ7rQb70NWkYPKLQrk6Mah8LMyXxnNogR1kzKoIVf3DCUfjArJoYVlrVLu3ruxNzN1jQb/ZdhxJimBO63mKTMGk1jSd3BXS+pYRbQ10OIyI5qTDwmU9erkmbm7qzsfvv3fby72rr/wzFUZrJgbKieAqBS8sK05SyxmNnqB6tP4AO3jOUOufcuQaxvUObNsAyRGtVplwoPD6vsnj13vzxzoGo/IC4V/cOCFVuNDD64XAVJzOLdiokIhT16uLhmMaO5q6CWaDZWwBb8LlefgB/d3UTnucPVkawcHztlF1jsjm3m5ksRtNxHNHNM1DDKprNg8pCyrk7PzW4fBj97oH5paAuRm5yZNVY07KnfazfN42S7yQ5hsSEYZrJA7OkPI3VgKWKHyoywrE2uDHzyw5IkPW+ctyPNhsdW4oAOEgh5K8KMV9f1v3RPKWrX7youX1blcetZ7UwdslsfKavPM3Nbbdj2yAbAvnzx6o3doqqeV+4AyA1ILHymWAhCYN/fCEALpokI69vyeTbW9U9tx4c1PPuh0X939h1qnWqq5pUq0LCuTZ+a2VEeCLx2a6q+G3HeGZMl6j1t4p9X4IsGfZKiDp1avTp79/P7Kjp11TeL82SvdfS8vaP+TvZZlpXl6bkuqvH/iSLc/M91ThDD3k3+4u+ZBpQuHEjLwQrtxEQQ02bmJCOaABKo1qpNnP9te2bGrpkl8PPtnd9+eq7o7jCnb70yVI/jyzHQ/qQfETjxSQsP5gi8qvlXpLFlsNb4028WA0blvHkAE24abVcdn59vVBx4aQxJ/nzt9rffma9cmTn+6OcN+4ki3NzO9PGS5bU/ZP6JKOk9WX+mgCgEHvk4Z5hGathhsKMi2rFVHjSVe/2sljE9WVOfHjywtvz3dB5w2lHyHHEFTRzvapjJUlFEek5Am8C0WDt203NWSHavEfHyGsVoxPkK2FLyPyn0nsO0Js8TmA1zPNiaTsQZFaATGEz4stJrfKbFgMDbLV28/7nhgHtXq1YlTF+6oPvjouv7xw73lmdf7OWmfnGmBHSpL90LsimipVy4itj4DgcYPeZHQdcxfUDPyLSnPewlc1irrn3p67OZHp/4ZuppVbdPQLDxNUcTQtYUILXdqKRJkLWg1frTZrZsL50t8IcEWjElndmq7/SjkGABpX0SlQMQ8AGe151o1I5wmAepbMsgLCPwkUYdNzt76ZYRKmsezWrPbNILYEONCBiBgMjOTsvVaNCoCaUBr//ALfLPQbv6aFg78aEAVORljtu/yaZDYD5HVW5GdS+6HhVWLdMJZ8c46TYgoijIVLWj+Nsp+0665lI1Zn46+bPjUHK7dUI5OQ0XLmA59J5o77K4ApJETwxFebDd+iUKY3WxyMa2aKuwXkknRrdN/cNiuFzGMbA0DF7zZqdcmNG19bsMaJPpYSE4w0RM+ZGxi5fXal1MMHf8+aRuQGzq6imldKD46x4YNtzSGyRgj8ReF5vjK1sa2WOV5Yip1kfSdQEevouAIgJi+JSkiqu0h5NZuE1xmvBFRqZCUkFVAtBwHg8c0lyvtRmtAvI9FHofdrJpieT13X8i/iLRGU5QHMraP2KzLzT3PTIjoJkX6Ksrglc2Xer//D+hurEFF6SwNAAAAAElFTkSuQmCC");
    background-size: 100% 100%;
    border: none;
  }
}

.checkbox-icon {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}