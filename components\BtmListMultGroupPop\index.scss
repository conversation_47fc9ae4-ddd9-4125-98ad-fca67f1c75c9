/* components/BtmListMultGroupPop/index.scss */

.btm-list-wrap {
  background-color: #fff;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #f0f0f0;
}

.header {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;

  .title {
    font-size: 32rpx;
    font-weight: 600;
    color: #20263a;
    margin-bottom: 8rpx;
  }

  .selected-count {
    font-size: 24rpx;
    color: #999;
  }
}

.list-container {
  flex: 1;
  height: 600rpx;
  max-height: 600rpx;
}

.list-item {
  padding: 0 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f8f8;
  }

  &.active {
    background-color: rgba(231, 36, 16, 0.05);

    .item-name {
      color: #e72410;
      font-weight: 500;
    }

    .item-checkbox {
      border-color: #e72410;
      background-color: #e72410;
      color: #fff;
    }
  }
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  min-height: 88rpx;
}

.item-name {
  flex: 1;
  font-size: 28rpx;
  color: #20263a;
  line-height: 40rpx;
  margin-right: 24rpx;
  word-break: break-all;

  // 文本溢出处理
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;

  &.checked {
    border-color: #e72410;
    background-color: #e72410;
  }
}

.checkbox-icon {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}