/**
 * 弹窗管理工具
 * 统一管理弹窗状态和操作
 */

const {
  POPUP_FIELD_MAPPING,
} = require('../config/fields');

/**
 * 弹窗管理器
 */
const PopupManager = {

  /**
   * 打开指定弹窗
   * @param {string} popupType - 弹窗类型
   * @returns {Object} 更新数据
   */
  openPopup(popupType) {
    const config = POPUP_FIELD_MAPPING[popupType];
    if (!config || !config.popupKey) {
      console.warn('未找到弹窗配置:', popupType);
      return {};
    }

    return {
      [config.popupKey]: true
    };
  },

  /**
   * 关闭指定弹窗
   * @param {string} popupType - 弹窗类型
   * @returns {Object} 更新数据
   */
  closePopup(popupType) {
    const config = POPUP_FIELD_MAPPING[popupType];
    if (!config || !config.popupKey) {
      console.warn('未找到弹窗配置:', popupType);
      return {};
    }
    return {
      [config.popupKey]: false
    };
  },

  /**
   * 关闭所有弹窗
   * @returns {Object} 更新数据
   */
  closeAllPopups() {
    const updateData = {};
    Object.values(POPUP_FIELD_MAPPING).forEach(config => {
      updateData[config.popupKey] = false;
    });
    return updateData;
  },
  /**
   * 获取弹窗配置
   * @param {string} popupType - 弹窗类型
   * @returns {Object|null} 弹窗配置
   */
  getPopupConfig(popupType) {
    return POPUP_FIELD_MAPPING[popupType] || null;
  },
};

module.exports = PopupManager;