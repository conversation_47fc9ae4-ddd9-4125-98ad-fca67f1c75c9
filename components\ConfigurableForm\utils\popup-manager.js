/**
 * 弹窗管理工具
 * 统一管理弹窗状态和操作
 */

const {
  POPUP_FIELD_MAPPING
} = require('../config/fields');

/**
 * 弹窗管理器
 */
const PopupManager = {
  /**
   * 获取弹窗状态映射
   * @param {Object} data - 组件数据
   * @returns {Object} 弹窗状态映射
   */
  getPopupStates(data) {
    const states = {};
    Object.values(POPUP_FIELD_MAPPING).forEach(config => {
      states[config.type] = data[config.visible] || false;
    });
    return states;
  },

  /**
   * 打开指定弹窗
   * @param {string} popupType - 弹窗类型
   * @returns {Object} 更新数据
   */
  openPopup(popupType) {
    const config = POPUP_FIELD_MAPPING[popupType];
    if (!config || !config.popupKey) {
      console.warn('未找到弹窗配置:', popupType);
      return {};
    }

    return {
      [config.popupKey]: true
    };
  },

  /**
   * 关闭指定弹窗
   * @param {string} popupType - 弹窗类型
   * @returns {Object} 更新数据
   */
  closePopup(popupType) {
    const config = POPUP_FIELD_MAPPING[popupType];
    if (!config || !config.popupKey) {
      console.warn('未找到弹窗配置:', popupType);
      return {};
    }

    return {
      [config.popupKey]: false
    };
  },

  /**
   * 关闭所有弹窗
   * @returns {Object} 更新数据
   */
  closeAllPopups() {
    const updateData = {};
    Object.values(POPUP_FIELD_MAPPING).forEach(config => {
      updateData[config.visible] = false;
    });
    return updateData;
  },

  /**
   * 切换弹窗状态
   * @param {string} popupType - 弹窗类型
   * @param {Object} currentData - 当前数据
   * @returns {Object} 更新数据
   */
  togglePopup(popupType, currentData) {
    const config = POPUP_FIELD_MAPPING[popupType];
    if (!config || !config.popupKey) {
      console.warn('未找到弹窗配置:', popupType);
      return {};
    }

    const isOpen = currentData[config.popupKey];
    return {
      [config.popupKey]: !isOpen
    };
  },

  /**
   * 获取弹窗配置
   * @param {string} popupType - 弹窗类型
   * @returns {Object|null} 弹窗配置
   */
  getPopupConfig(popupType) {
    return POPUP_FIELD_MAPPING[popupType] || null;
  },

  /**
   * 检查弹窗是否打开
   * @param {string} popupType - 弹窗类型
   * @param {Object} data - 组件数据
   * @returns {boolean} 是否打开
   */
  isPopupOpen(popupType, data) {
    const config = POPUP_FIELD_MAPPING[popupType];
    if (!config || !config.popupKey) {
      return false;
    }
    return data[config.popupKey] || false;
  },

  /**
   * 批量设置弹窗状态
   * @param {Object} popupStates - 弹窗状态映射 {popupType: boolean}
   * @returns {Object} 更新数据
   */
  setPopupStates(popupStates) {
    const updateData = {};
    Object.keys(popupStates).forEach(popupType => {
      const config = POPUP_FIELD_MAPPING[popupType];
      if (config && config.popupKey) {
        updateData[config.popupKey] = popupStates[popupType];
      }
    });
    return updateData;
  },

  /**
   * 获取弹窗数据字段映射
   * @param {string} popupType - 弹窗类型
   * @returns {string|null} 数据字段名
   */
  getDataField(popupType) {
    const config = POPUP_FIELD_MAPPING[popupType];
    return config ? config.data : null;
  },

  /**
   * 创建弹窗事件处理器
   * @param {Object} context - 组件上下文
   * @returns {Object} 事件处理器
   */
  createEventHandlers(context) {
    return {
      /**
       * 处理弹窗打开
       */
      handlePopupOpen(e) {
        const item = e.currentTarget.dataset.item;
        const updateData = PopupManager.openPopup(item.type);

        if (Object.keys(updateData).length > 0) {
          context.setData(updateData);
        }
      },

      /**
       * 处理弹窗关闭
       */
      handlePopupClose(e) {
        const popupType = e.currentTarget.dataset.type;
        const updateData = PopupManager.closePopup(popupType);

        if (Object.keys(updateData).length > 0) {
          context.setData(updateData);
        }
      },

      /**
       * 处理弹窗提交
       */
      handlePopupSubmit(e) {
        const {
          checkedList: obj,
          mark
        } = e.detail;
        const popupConfig = POPUP_FIELD_MAPPING[mark];

        if (!popupConfig) return;

        // 更新数据并关闭弹窗
        const updateData = {
          [`params.${popupConfig.data}`]: obj,
          ...PopupManager.closeAllPopups()
        };

        context.setData(updateData);

        // 触发结果更新
        if (context.result) {
          context.result();
        }
      }
    };
  }
};

module.exports = PopupManager;