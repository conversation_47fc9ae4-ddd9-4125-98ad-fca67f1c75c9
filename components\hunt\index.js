import {
  renderList,
  params
} from './mixin.js'
var comMixin = require('./common/common.js')

Component({
  behaviors: [comMixin],
  data: {
    itemList: JSON.parse(JSON.stringify(renderList)), // 页面静态数据
    leftList: JSON.parse(JSON.stringify(renderList)), // 页面静态数据-右边
  },
  methods: {
    clearSear() { // 清空模版
      // 后面copy组件的时候renderList名字会跟着换
      let list = renderList.map(item => {
        if (item.isOpenIcon) {
          item.isOpen = this.data.itemList.filter(i => item.type === i.type)[0]?.isOpen
        }
        return item
      })
      this.setData({
        itemList: JSON.parse(JSON.stringify(list)),
        params: JSON.parse(JSON.stringify(params)),
        minCapital: '',
        maxCapital: '',
        minDate: '',
        maxDate: '',
        date: '',
        capitalActive: false,
        socialActive: false,
        socialminPeson: '',
        socialmaxPeson: '',
        dateActive: false,
        dateType: ''
      }, () => {
        // console.log(this.data.params)
      })
    },
  },
  pageLifetimes: {
    show() {
      let {
        wrapHeight,
        isIphoneX
      } = this.data
      // 计算容器高度 
      if (!wrapHeight) {
        this.setData({
          wrapHeight: `calc(100vh - ${isIphoneX ? '168rpx' : '110rpx'})`
        })
      }
      this.setBackfillData(this.data.paramsData)
    }
  },
  observers: {
    vipVisible(val) {
      // 通过事件传递的方式告诉外面，需要vip弹窗
      console.log('是否需要弹vip弹窗', val)
      // todo 25.6 后续放开
      // this.triggerEvent('vip', true)
    }
  }
})