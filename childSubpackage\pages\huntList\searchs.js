import {
  home,
  common
} from '../../../service/api';
import {
  getHeight
} from '../../../utils/height';
import {
  preventActive
} from '../../../utils/util';
import {
  getPx,
  formatDate,
  handleSearchHight
} from '../../../utils/formate';
import {
  handlestructure,
  getNameFromPop,
  handleMultiple
} from '../../../components/hunt/mixin';
import {
  hasPrivile
} from '../../../utils/route'
import {
  collect
} from '../../../utils/mixin/collect'
const app = getApp();
let PAGESIZE = 10
Page({
  data: {
    // 搜索相关
    historyList: [],
    browsingHistory: [], //浏览历史
    timer: null,
    // 弹窗相关 
    popType: '',
    showVisible: false, //是否显示弹窗 
    title: '',
    content: '',
    cancelBtnText: "取消",
    confirmBtnText: "删除",
    // 浏览历史高度 
    scrollHeight: 'auto',
    filtrateHeight: 'auto',
    statusBarHeight: 'auto',
    // 筛选 
    dropDownMenuTitle: ['全国', '全部行业', '更多筛选'],
    // 卡片 下拉加载更多 
    cardHeight: '500',
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: PAGESIZE, //每页多少条
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态

    // 弹窗相关 
    popType: '',
    showVisible: false, //是否显示弹窗 
    title: '',
    content: '',
    cancelBtnText: "取消",
    confirmBtnText: "删除",
    cnacelEnt: '', //缓存取消收藏企业id
    popIndex: 0, //缓存取消收藏企业下标
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: '',
    },
    locationMap: {},
    // 高级搜索参数 
    heightParams: {},
    isHeightParams: false
  },
  onShow() {
    this.scrollH()
  },
  onLoad(options) {
    let {
      dropDownMenuTitle,
      bazaarParms,
      isHeightParams,
    } = this.data
    let heightParams, title1 = '全国',
      title2 = '全部行业';
    heightParams = options?.str
    // 说明是从高级搜索那边过来的 
    heightParams = JSON.parse(decodeURIComponent(heightParams))
    let temObj = this.handleHeightParams(heightParams, true)
    // 设置标题
    if (heightParams.regionData?.length > 0) {
      // title1 = heightParams.regionData.sort((a, b) => (a.level - b.level)).map(i => i.name).join(',').slice(0, 4)
      let str = getNameFromPop(heightParams.regionData)
      title1 = str.length >= 4 ? str.slice(0, 4) + '...' : str;
      dropDownMenuTitle[0] = title1
    }
    if (heightParams.eleseic_data?.length > 0) {
      let str = getNameFromPop(heightParams.eleseic_data)
      title2 = str.length >= 4 ? str.slice(0, 4) + '...' : str;
      // title2 = heightParams.eleseic_data.sort((a, b) => (a.level - b.level)).map(i => i.name).join(',').slice(0, 4)
      dropDownMenuTitle[1] = title2
    }
    this.setData({
      heightParams,
      dropDownMenuTitle,
      bazaarParms: {
        ...bazaarParms,
        ...temObj
      },
      isHeightParams,
    }, () => {
      this.initGetList()
    })
  },
  handleHeightParams(heightParams, isTemplate) { //格式化高级筛选的参数
    // console.log('heightParams', heightParams)
    let params = Object.assign({}, heightParams) //拷贝
    // 2023.5.12 在组装一次
    if (isTemplate) {
      params['areas'] = params.regionData?.length > 0 ? handleMultiple(params.regionData).filter(i => i.status === 'checked').map(i => i.code) : []
      params['trade_types'] = params.eleseic_data?.length > 0 ? handleMultiple(params.eleseic_data).filter(i => i.status === 'checked').map(i => i.code) : []
      params['ent_type'] = params.enttype_data?.length > 0 ? handleMultiple(params.enttype_data).filter(i => i.status === 'checked').map(i => i.code) : []
      params['enterprise_license_list'] = params.all_cert_data?.length > 0 ? handleMultiple(params.all_cert_data).filter(i => i.status === 'checked').map(i => i.code) : []
      params['chain_codes'] = params.chain_codes_data?.length > 0 ? params.chain_codes_data.filter(i => i.active === true).map(i => i.code) : []
    }
    // 高级筛选-第一次请求-合并参数
    delete params['all_cert_data']
    delete params['eleseic_data']
    delete params['enttype_data']
    delete params['regionData']
    delete params['chain_codes_data']
    params.contacts_style = params.contacts_style || [];
    params.fixed_phone_flag = params.fixed_phone_flag || [];

    params['fixed_phone_flag'] = Array.from(new Set([...params.fixed_phone_flag, ...params.contacts_style]))

    delete params['contacts_style']
    // 联系方式为none则不传
    let idx = params['fixed_phone_flag'].indexOf('none')
    if (idx > -1) {
      params['fixed_phone_flag'].splice(idx, 1)
    }
    params = isTemplate ? handlestructure(params) : params;
    return params
  },
  //获取小米列表
  initGetList(callback) {
    const that = this;
    let {
      bazaarlist,
      bazaarHasData,
      bazaarParms
    } = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    // 计算卡片高度
    this.cardHeight()
    //
    home.portrait(bazaarParms).then(res => {
      let items = res?.items || [],
        count = res?.count || 0;
      let ary = []
      if (items.length < bazaarParms.page_size) bazaarHasData = false;
      ary = items.map((item, index) => {
        item.register_date = formatDate(item.register_date, 'yyyy-MM-dd')
        item.heightKey = bazaarParms['ent_name']
        return item;
      })
      ary = handleSearchHight(ary, 'ent_name', bazaarParms['ent_name'])
      that.setData({
        bazaarlist: bazaarlist.concat(ary),
        count: count,
        bazaarIsFlag: true
      }, () => {
        if (!that.data.bazaarlist.length) that.setData({
          bazaarIsNull: true,
          bazaarHasData: true
        });
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      console.log(err)
    })
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    wx.showNavigationBarLoading()
    let {
      bazaarParms
    } = that.data
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: PAGESIZE
    }
    that.setData({
      bazaarParms: obj,
      bazaarlist: [],
      bazaarHasData: true,
    }, () => that.initGetList(() => {
      that.setData({
        bazaarIsTriggered: false
      })
      wx.hideNavigationBarLoading()
    }))
  },
  //加载更多
  async bazaarloadMore() {
    // 判断vip类型 主要是针对普通用户 
    if (this.data.bazaarlist.length >= 20) {
      let permission = await hasPrivile({
        packageType: true
      })
      this.setData({
        permission,
      })
      if (permission == '普通VIP') return;
    }


    let {
      bazaarParms,
      bazaarHasData,
      bazaarIsFlag
    } = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData({
      bazaarParms
    }, () => this.initGetList());
  },
  // 筛选
  onFlitter(e) {
    let {
      dropDownMenuTitle,
      bazaarParms,
      bazaarlist,
      heightParams,
    } = this.data
    const obj = e.detail;
    dropDownMenuTitle[0] = obj.name1
    dropDownMenuTitle[1] = obj.name2
    let isFilter = obj.isFilter
    delete obj['name1'];
    delete obj['name2'];
    delete obj['isFilter'];
    // 因为obj有部分删除了 不能直接...bazaarParms
    let oldbazaarParms = JSON.parse(JSON.stringify(bazaarParms))
    bazaarParms = {
      page_index: 1,
      page_size: PAGESIZE,
      ...obj
    }
    let temObj = {}
    if (isFilter) {
      temObj = Object.keys(heightParams).length > 0 ? this.handleHeightParams(bazaarParms) : bazaarParms;
    } else {
      temObj = {
        ...oldbazaarParms,
        trade_types: obj['trade_types'],
        areas: obj['areas'],
        page_index: 1,
        page_size: PAGESIZE
      }
    }
    bazaarlist = []
    this.setData({
      bazaarParms: temObj,
      bazaarlist,
      dropDownMenuTitle
    }, () => {
      app.showLoading('加载中...')
      // console.log(this.data.bazaarParms)
      this.initGetList(() => wx.hideLoading())
    })
  },
  //----------
  // 动态获取页面高度 
  cardHeight() {
    var that = this;
    getHeight(that, ['.drop-menu'], (data) => {
      const {
        screeHeight,
        res
      } = data
      let h1 = res[0]?.top
      let h2 = res[0]?.height
      this.setData({
        cardHeight: screeHeight - h1 - h2 - getPx(96)
      })
    })
  },
  scrollH() {
    var that = this;
    getHeight(that, ['.searchs', '.search_a', '.drop-menu', '.his_title'], (data) => {
      const {
        screeHeight,
        res,
        statusBarHeight
      } = data
      let h1 = res[0]?.height || 0
      let h2 = res[1]?.height || 0
      let h4 = res[3]?.height || 0
      // 处理search外剩余的高度 
      let filtrateHeight = screeHeight - h1
      // 浏览历史的滚动高度 
      let scrollHeight = screeHeight - h1 - h2 - h4
      that.setData({
        scrollHeight: scrollHeight,
        filtrateHeight: filtrateHeight,
        statusBarHeight: statusBarHeight,
      })
    })
  },
  // 卡片点击回调 
  goto(e) {
    const {
      item
    } = e.currentTarget.dataset
    console.log('点击跳转到详情')

  },
  async onCard(data) {
    let that = this
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      let {
        bazaarlist
      } = this.data
      const type = data.detail.type
      const comDetail = data.detail.data
      const index = bazaarlist.findIndex(item => item.ent_id == comDetail.ent_id) //comDetail.index
      // console.log(index)
      // 处理收藏 
      // return
      if (type == 'collect') {
        collect(that, comDetail, 'bazaarlist')
      } else if (type == 'relation') {
        let contactRes = await common.contact(comDetail.ent_id, encodeURI("types=1,2"))
        console.log("联系方式res", contactRes);
        if (contactRes.length > 0) {
          this.setData({
            contactList: contactRes,
            showContact: true,
            activeEntId: comDetail.ent_id
          })
        } else {
          app.showToast('暂无联系方式', 'none', 1300)
          this.setData({
            contactList: [],
            showContact: false
          })
        }
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location.lat,
            lon: +comDetail.location.lon,
          },
          locationTxt: comDetail.register_address,
          addmarkers: [{
            id: 1,
            latitude: +comDetail.location.lat,
            longitude: +comDetail.location.lon,
            iconPath: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png",
            width: 20,
            height: 20,
          }],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location.lat, //维度
            longitude: +comDetail.location.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        })
      }
    })
  },
  goMap() {
    const {
      locationMap
    } = this.data
    wx.openLocation(locationMap)
  },
  onCloseContact() {
    this.setData({
      showContact: false
    })
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    })
  },
  makeCall(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
    // console.log(item.contact_data)
    wx.makePhoneCall({
      phoneNumber: item.contact_data,
    })
  },
  // 去详情页面 
  goDetail(e) {
    let {
      enterprise_id
    } = e.currentTarget.dataset.item
    const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${enterprise_id}`)
    // const url = encodeURIComponent(`http://***************:8081?entId=${enterprise_id}`)
    // const url = encodeURIComponent(`http://j-h5-report.ihdwork.com?entId=${enterprise_id}`)
    app.route(this, `/subPackage/pages/webs/index?url=${url}`)
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    })
  }
});