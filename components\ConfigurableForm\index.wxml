<view style="height:{{wrapHeight}};" class="searWrap">
  <!-- 左侧菜单 -->
  <scroll-view wx:if="{{!isHideLeft}}" scroll-y style="height:{{wrapHeight}};" scroll-with-animation class="searWrap-r ">
    <view style="height: auto;">
      <view class="searL" wx:for="{{leftList}}" wx:key="index">
        <view class="{{item.isActive&&'active'}}" wx:if="{{item.onlyText}}">
          <view class="tit" bindtap="closeleft" data-item="{{item}}">
            <view class="tit-text">{{item.title}}</view>
            <image class="tit-icon" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png" mode="aspectFill" wx:if="{{!item.isOpen}}"></image>
            <image class="tit-icon" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png" mode="aspectFill" wx:else></image>
          </view>
          <view class="tit_cont {{item.isOpen ? 'tit_cont--open' : 'tit_cont--close'}}">
            <view wx:for="{{item.map}}" wx:for-index="key" wx:for-item="itm" wx:key="key" class="child-item {{itm.active&&'child-active'}}" bindtap="leftactvie" data-item="{{item}}" data-itm="{{itm.key}}">{{itm.value}}</view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 右侧内容 -->
  <scroll-view scroll-y style="height:{{wrapHeight}};" scroll-with-animation class="searWrap-l {{!isHideLeft&&'noHideLeft'}}" scroll-into-view="{{idName}}">
    <view class="content">
      <view wx:for="{{itemList}}" wx:key="id" id="{{item.type}}" bindtap="closeInptPop">
        <!-- 输入框类型 -->
        <view class="content-item-pop cnt-ipt" wx:if="{{item.special=='input'}}" catchtap="_event">
          <view class="title" style="font-weight: {{idName==item.type?600:400}};">
            {{item.title}}
          </view>
          <view class="ipt">
            <input class="ipt-input" placeholder="{{item.placeholder || '请输入企业名称(非必填)'}}" placeholder-class="placeCls" value="{{params.ent_name}}" bindfocus="onFocus" bindblur="onBlur" bindinput="onInput" maxlength="{{item.maxLength || 24}}" />
          </view>

          <!-- 搜索建议 -->
          <view class="child-box" catchtouchmove="return" wx:if="{{focus&&params.ent_name.length>0}}">
            <scroll-view class="child-box-ul" scroll-y scroll-with-animation>
              <block wx:for="{{searchList}}" wx:key="index" wx:for-item="obj" wx:if="{{searchList.length>0}}">
                <view hover-class="hover">
                  <view class="search-li" catchtap="clickItem" data-item="{{obj}}">
                    <block wx:for="{{obj.ent_name}}" wx:key="index">
                      <text class="listtext {{item==params.ent_name ? 'searchHigh' : '' }}">{{item}}</text>
                    </block>
                  </view>
                </view>
              </block>
              <block wx:if="{{searchList.length<=0}}">
                <view class="empty">
                  --暂无数据--
                </view>
              </block>
            </scroll-view>
          </view>
        </view>

        <!-- 弹窗选择类型 -->
        <view class="content-item-pop {{item.betweenBoder&&'yxbglt1'}}" wx:elif="{{item.special=='pop'}}">
          <view class="title" style="font-weight: {{idName==item.type?600:400}};">
            {{item.title}}
          </view>
          <view class="content-item-pop-r" bindtap="handlePop" data-item="{{item}}">
            <text class="pop-text {{item.content&&'has'}}">{{item.content || '全部'}}</text>
            <image class="pop-icon" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home_arrow.png"></image>
          </view>
        </view>

        <!-- 标签选择类型 -->
        <view class="content-item {{item.hasBg&&'yxbg'}}  {{item.betweenTopBoder && 'yxbglt'}}  {{item.betweenBoder && 'yxbglt1'}}  {{item.bbBoder&&'yxbgend'}}" wx:else>
          <!-- 产业优选特殊背景 -->
          <view class="yxbgImg" wx:if="{{item.hasBg}}">
            <image class="bg-image" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/zsyx.png"></image>
          </view>

          <!-- 标题 -->
          <view class="tit-text" wx:if="{{item.onlyText}}">
            <view class="line-left" wx:if="{{item.title!='产业优选'}}"></view>
            {{item.title}}
            <view class="line-right" wx:if="{{item.title!='产业优选'}}"></view>
          </view>

          <view class="tit {{idName==item.type ? 'tit--active' : ''}}" wx:elif="{{!item.onlyText&&!item.isOpenIcon}}">
            {{item.title}}
            <text wx:if="{{item.icon}}" bindtap="onexpPop" class="filterexp"></text>
            <view wx:if="{{item.vip}}" class="vip"></view>
          </view>

          <view wx:elif="{{item.isOpenIcon}}" class="zhankai {{idName==item.type ? 'zhankai--active' : ''}}">
            <view style="display: flex; align-items: center;">
              {{item.title}}
              <view wx:if="{{item.vip}}" class="vip"></view>
            </view>
            <image class="zhankai-icon" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png" mode="aspectFill" wx:if="{{!item.isOpen}}" bindtap="openright" data-item="{{item}}"></image>
            <image class="zhankai-icon" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png" mode="aspectFill" wx:else bindtap="openright" data-item="{{item}}"></image>
          </view>

          <!-- 标签内容 -->
          <view class="wrap {{item.isOpenIcon && item.isOpen ? 'wrap--limited' : 'wrap--auto'}}">
            <block wx:if="{{item.list}}">
              <text wx:for="{{item.list}}" wx:key="id" wx:for-item="tag" class="tag-item {{tag.active&&'active'}}" bindtap="selectTag" data-id="{{tag.id}}" data-type="{{item.type}}" data-name="{{tag.name}}" data-item="{{item}}">{{tag.name}}</text>
            </block>

            <!-- 范围输入框 -->
            <view class="input-wrap" wx:if="{{item.genre=='input'}}">
              <view class="input-item {{capitalActive&&'active'}}" wx:if="{{item.type=='reg_capital'}}">
                <input class="input-field" type="number" placeholder="{{item.min}}" value="{{minCapital}}" bindinput="inputChange" data-type="{{item.type}}" bindfocus="inputFocus" data-type="{{item.type}}" model:value="{{minCapital}}" />
                <view class="year">{{item.unit}}</view>
              </view>
              <view class="short-line">-</view>
              <view class="input-item {{capitalActive&&'active'}}" wx:if="{{item.type=='reg_capital'}}">
                <input class="input-field" type="number" placeholder="{{item.max}}" value="{{maxCapital}}" bindinput="inputChange" data-type="{{item.type}}" bindfocus="inputFocus" data-type="{{item.type}}" model:value="{{maxCapital}}" />
                <view class="year">{{item.unit}}</view>
              </view>

              <!-- 从业人数输入 -->
              <view class="input-item {{socialActive&&'active'}}" wx:if="{{item.type=='insured_num'}}">
                <input class="input-field" type="number" placeholder="最少人数" value="{{socialminPeson}}" bindinput="inputChange" data-type="{{item.type}}" bindfocus="inputFocus" data-type="{{item.type}}" model:value="{{socialminPeson}}" />
                <view class="year">人</view>
              </view>
              <view class="short-line" wx:if="{{item.type=='insured_num'}}">-</view>
              <view class="input-item {{socialActive&&'active'}}" wx:if="{{item.type=='insured_num'}}">
                <input class="input-field" type="number" placeholder="最多人数" value="{{socialmaxPeson}}" bindinput="inputChange" data-type="{{item.type}}" bindfocus="inputFocus" data-type="{{item.type}}" model:value="{{socialmaxPeson}}" />
                <view class="year">人</view>
              </view>
            </view>

            <!-- 日期输入框 -->
            <view class="input-wrap" wx:if="{{item.genre=='tpop'}}">
              <view class="input-item {{dateActive&&'active'}}">
                <input class="input-field" placeholder="{{item.min}}" value="{{minDate}}" bindfocus="inputFocus" data-type="{{item.type}}" bindtap="showtDatePicker" data-type="startDate" />
                <view class="year">{{item.unit}}</view>
              </view>
              <view class="short-line">-</view>
              <view class="input-item {{dateActive&&'active'}}">
                <input class="input-field" placeholder="{{item.max}}" value="{{maxDate}}" bindfocus="inputFocus" data-type="{{item.type}}" bindtap="showtDatePicker" data-type="endDate" />
                <view class="year">{{item.unit}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>

<!-- 弹窗组件 -->
<!-- 地区弹窗 -->
<multiplec-choice visible="{{regionPop}}" mark="areas" dataType="districtAry" bindsubmits="submitSub" oldData="{{params.regionData}}"></multiplec-choice>

<!-- 行业弹窗 trade_types-->
<multiplec-choice visible="{{industry_code_list_pop}}" mark="trade_types" dataType="eleseicAry" bindsubmits="submitSub" oldData="{{params.eleseic_data}}"></multiplec-choice>

<!-- 企业类型弹窗 ent_type-->
<multiplec-choice visible="{{enttypePop}}" mark="ent_type" dataType="enttypeAry" bindsubmits="submitSub" oldData="{{params.enttype_data}}"></multiplec-choice>

<!-- 企业许可弹窗 -->
<multiplec-choice visible="{{districtPop}}" mark="enterprise_license_list" dataType="allCertAry" bindsubmits="submitSub" oldData="{{params.all_cert_data}}"></multiplec-choice>

<!-- 日期选择器 -->
<DatePicker visible="{{datePop}}" date="{{date}}" bindconfirm="setDate" bindcancel="setData" data-datePop="{{false}}"></DatePicker>