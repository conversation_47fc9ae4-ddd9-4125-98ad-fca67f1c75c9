const mockData = {
  "ent_name": "大昌建设集团有限公司",
  "industry_code_list": [{
    "code": "A011",
    "name": "谷物种植",
    "parent": "A01",
    "level": "3",
    "ischildren": false,
    "status": "checked"
  }, {
    "code": "A01",
    "name": "农业",
    "parent": "A",
    "level": "2",
    "ischildren": false,
    "status": "semi-checked",
    "active": true
  }, {
    "code": "A",
    "name": "农、林、牧、渔业",
    "parent": "",
    "level": "1",
    "ischildren": false,
    "status": "semi-checked",
    "active": true
  }],
  "area_code_list": [{
    "code": "110101",
    "name": "东城区",
    "parent": "110000",
    "level": "3",
    "ischildren": true,
    "status": "checked",
    "isarea": true,
    "active": true
  }, {
    "code": "110000",
    "name": "北京市",
    "parent": "",
    "level": "1",
    "ischildren": false,
    "status": "semi-checked",
    "active": true
  }],
  "head_ent_flag": false,
  "ent_expand": ["B"],
  "ent_size": ["A"],
  "benefits_assess": ["A"],
  "industrial_list": [{
    "code": "H1",
    "name": "数字与时尚产业",
    "status": "checked",
    "active": true
  }, {
    "code": "H1A1",
    "name": "数字创意产业",
    "status": "checked",
    "active": true
  }],
  "technology": ["HN"],
  "est_date": [{
    "start": "2022-06-05",
    "end": "2023-06-05",
    "name": "2-3年"
  }],
  "reg_capital": [{
    "start": "1000",
    "end": "5000"
  }],
  "ent_status_list": ["2"],
  "enterprise_license_list": [{
    "code": "A33",
    "name": "网信办",
    "parent": "A",
    "level": "2",
    "ischildren": false,
    "status": "checked",
    "active": true
  }, {
    "code": "A",
    "name": "IT、信息传输相关",
    "parent": "",
    "level": "1",
    "ischildren": false,
    "status": "semi-checked",
    "active": true
  }, {
    "code": "A3301",
    "name": "计算机信息系统安全产品专用许可证",
    "parent": "A33",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "A3302",
    "name": "境内区块链信息服务备案",
    "parent": "A33",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "A3303",
    "name": "信息安全测评服务资质",
    "parent": "A33",
    "level": "3",
    "ischildren": false,
    "status": "checked"
  }, {
    "code": "A3304",
    "name": "信息安全测评",
    "parent": "A33",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "A3305",
    "name": "软件产品证书",
    "parent": "A33",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "A3306",
    "name": "软件企业认定证书",
    "parent": "A33",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "A3307",
    "name": "软件产品登记证书",
    "parent": "A33",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }],
  "ent_type": [{
    "code": "10000",
    "name": "其他",
    "parent": "A",
    "level": "2",
    "ischildren": false,
    "status": "checked",
    "active": true
  }, {
    "code": "A",
    "name": "工商主体",
    "parent": "",
    "level": "1",
    "ischildren": false,
    "status": "semi-checked",
    "active": true
  }, {
    "code": "10100",
    "name": "分公司分公司",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10200",
    "name": "股份合作公司分公司",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10300",
    "name": "中外合资企业",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10400",
    "name": "非国有独资有限责任公司分公司（私营）",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10500",
    "name": "外商独资企业",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10600",
    "name": "其他分公司",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10700",
    "name": "集体经济",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10800",
    "name": "社会团体",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "10900",
    "name": "国有经济",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }, {
    "code": "11000",
    "name": "股份有限公司分公司（国有控股）",
    "parent": "10000",
    "level": "3",
    "ischildren": true,
    "status": "checked"
  }],
  "ent_entity_type": ["3"],
  "mobile_phone_flag": false,
  "fixed_phone_flag": false,
  "email_flag": false,
  "insured_num": [{
    "start": "5000",
    "end": ""
  }],
  "financing_info_list": ["A"],
  "listing_status": ["B"],
  "tendering_and_bidding": false,
  "job_flag": false,
  "patent_info": ["1"],
  "trade_mark_info_flag": false,
  "copyrights_flag": false,
  "copyright_of_works": false,
  "official_website_info": false,
  "icp_filing": false,
  "android_app": false,
  "apple_app": false,
  "applet": false,
  "we_chat_official_account": false,
  "weibo_flag": false,
  "judgment_doc": true,
  "administrative_penalty": true,
  "chattel_mortgage": true,
  "business_abnormalities": true
}
module.exports = {
  mockData
}