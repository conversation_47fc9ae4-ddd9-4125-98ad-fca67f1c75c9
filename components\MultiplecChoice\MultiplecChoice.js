import { getData } from './utils'
import constant from '../../utils/constant'
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {

    // 弹出层显示/隐藏
    visible: {
      type: Boolean,
      value: false
    },
    position: {
      type: String,
      value: "bottom",
    },
    startDistance: {
      type: String,
      value: "0px",
    },

    // 上一次选中的数据，用于回填
    oldData: {
      type: Array,
      value: []
    },

    // 省级是否单选
    provinceSingle: {
      type: Boolean,
      value: false
    },
    zIndex: {
      type: Number,
      value: 0
    },
    dataType: {
      type: String,
      value: constant.DistrictAry
    },
    mark: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    regionObj: {
      provinceList: [], // 省列表
      curCityList: [], // 当前选中省的市列表
      curAreaList: [], // 当前选中市的区列表
    }, // 地区对象
    cityObj: {}, //缓存不同省的市列表
    areaObj: {}, // 缓存不同市的区列表
    checkedList: [], // 选中列表

  },

  lifetimes: {
    attached() {
      this.getRegion();
    },
  },
  pageLifetimes: {
    show() {
      // console.log(app.globalData.login)
      this.getRegion(); //搜索页面渲染了但是要登录才送
    }
  },
  observers: {
    // 监听区级列表状态改变
    'regionObj.curAreaList': function (areaList) {
      // 市级状态根据区级状态改变
      this.changeParentStatus(areaList, 'area');
    },

    // 监听市级列表状态改变
    'regionObj.curCityList': function (cityList) {
      // 省级状态根据市级状态改变
      this.changeParentStatus(cityList, 'city');
    },

    // 通过监听弹出层的显示来进行上一次选中数据的回填
    visible: function (bl) {
      bl && this.backFillRegion();
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {

    // 根据回填数据设置选中状态
    setBackFillStatus(arrData = [], code, status) {
      return arrData.map(region => {
        if (region.code === code) {
          region['status'] = status
        };
        return region
      })
    },

    // 数据回填
    backFillRegion() {
      let { oldData, regionObj, cityObj, areaObj, checkedList } = this.data;
      this.reset()
      // console.log('原始的数据源', oldData, regionObj, cityObj, areaObj, checkedList)
      for (let region of oldData) {
        let { level, code, status, parent, isarea } = region;
        if (level === '1') {
          // 特殊情况 -第一个单独处理
          if (code === regionObj['provinceList'][0].code && status === 'checked') {
            cityObj[code].forEach(item => item.status = 'checked')
            regionObj['curCityList'].forEach(item => {
              if (item.parent == code) {
                item.status = 'checked'
              }
            })
            regionObj['provinceList'] = this.setBackFillStatus(regionObj['provinceList'], code, status)
          } else {
            regionObj['provinceList'] = this.setBackFillStatus(regionObj['provinceList'], code, status)
          }
        } else if ((level === '2' || (level === '3' && isarea)) && cityObj[parent]?.length) {
          // 特殊情况 
          cityObj[parent] = this.setBackFillStatus(cityObj[parent], code, status)
        } else if (areaObj[parent]?.length) {
          areaObj[parent] = this.setBackFillStatus(areaObj[parent], code, status)
        }
      }
      checkedList = JSON.parse(JSON.stringify(oldData));
      // console.log('原始的数据源', oldData, regionObj, cityObj, areaObj, checkedList)
      // console.log('checkedList', checkedList);
      this.setData({ regionObj, cityObj, areaObj, checkedList })
    },


    // 重置选中状态
    resetStatus(obj) {
      for (let key in obj) {
        obj[key] = obj[key].map(region => {
          region.status = '';
          return region;
        })
      }
      return obj;
    },

    // 重置所有数据状态
    reset() {
      let { regionObj, cityObj, areaObj, checkedList } = this.data;
      // 省级状态重置
      regionObj = this.resetStatus(regionObj);
      // 市级备份状态重置
      cityObj = this.resetStatus(cityObj);

      // 区级备份状态重置
      areaObj = this.resetStatus(areaObj);

      // 选中列表重置
      checkedList = [];
      this.setData({ checkedList })
    },

    // 弹出层关闭时清空数据选中状态
    close() {
      let { regionObj, cityObj, areaObj, checkedList } = this.data;
      this.reset();
      this.setData({ regionObj, cityObj, areaObj });
      this.triggerEvent('close', checkedList) // 向外传递一个自定义事件
    },

    // 将选中数据通过自定义事件传递出去
    submit() {
      let { checkedList } = this.data;
      // console.log(this.data.checkedList, this.data.cityObj, this.data.areaObj)
      // 
      console.log('checkedList111', checkedList);
      this.triggerEvent('submit', checkedList) // 向外传递一个自定义事件
      this.triggerEvent('submits', { checkedList, mark: this.data.mark }) // 扩展
    },

    // 子级状态改变更新父级状态
    changeParentStatus(list, type) {
      let { regionObj: { provinceList, curCityList }, regionObj } = this.data;
      let temp = type === 'area' ? curCityList : provinceList;
      let checkedArr = [], // 存储选中状态的数组
        semiCheckedArr = [], // 存储半选状态的数组
        parentCode = ''; // 父级的code
      for (let item of list) {
        parentCode = item.parent;
        if (item.status === 'checked') checkedArr.push(item.status);
        if (item.status === 'semi-checked') semiCheckedArr.push(item.status);
      }
      temp = temp?.map(item => {
        if (item.code === parentCode) {
          let len = checkedArr.length,
            semiLen = semiCheckedArr.length;
          item.status = len === list.length ? 'checked' :
            semiLen ? 'semi-checked' :
              len === 0 ? '' : 'semi-checked';
          this.setCheckedList(item); // 设置选中项数据
        }
        return item;
      })
      let region = type === 'area' ? 'regionObj.curCityList' : 'regionObj.provinceList'
      this.setData({ [region]: temp });
    },

    // // 切换时更新活动状态
    changeRegion(e) {
      this.getChildData(e.currentTarget.dataset);
    },

    // 设置当前数据状态，获取下一级数据
    getChildData(obj) {
      let { code, level, active, status } = obj; // active：当前点击节点的状态
      let { regionObj, cityObj, areaObj } = this.data;
      let sendRequest = true; // 是否发送请求获取数据
      let key = level === '1' ? 'provinceList' : 'curCityList';
      if (!active) regionObj.curAreaList = []; // 每次切换清空市下面的区列表
      // 当前选中项添加active样式
      // 
      regionObj[key] = regionObj[key].map(item => {
        item.active = false;
        if (item.code === code) item.active = true;
        return item;
      })

      // 切换省级的时候重置市级的选中状态
      if (level === '1' && !active) {
        regionObj.curCityList = regionObj.curCityList.map(city => {
          city.active = false;
          return city;
        })
      }

      // 如果点击的省，并且已经缓存过该省下面的市列表，则从缓存获取市数据
      if (level === '1' && code in cityObj) {
        regionObj.curCityList = cityObj[code];
        sendRequest = false;
      }

      // 如果点击的市，并且已经缓存过该市下面的区列表，则从缓存获取区数据
      if (level === '2' && code in areaObj) {
        regionObj.curAreaList = areaObj[code];
        sendRequest = false;
      }

      // 如果存在市是区的level的情况，当做区处理
      if (level === '3') sendRequest = false;
      this.setData({ regionObj });

      // 发送请求获取对应数据
      sendRequest && this.getRegion(code, level, status)
    },
    handleDataList(item, parentCode, level, status, oldData) {
      oldData.forEach(element => {
        if (element.code == item.code) {
          item.status = element.status
        }
      });
      // console.log(item)
    },
    //获取地区
    async getRegion(code = '', level, status = '') {
      // if (!app.globalData.login) return;

      let { regionObj, cityObj, areaObj, checkedList, dataType } = this.data;
      let temp = '';
      temp = !level ? 'provinceList' : level === '1' ? 'curCityList' : 'curAreaList';
      // let dataList = await common.getDistrict({ code });
      let dataList = await getData({ code, dataType })
      // console.log(dataList)
      regionObj[temp] = dataList.map(item => {
        if ((level == '1' || level == '2') && (status == 'semi-checked')) {
          // 回调的特殊情况 当level为1或者2 ,status为'semi-checked' 并且oldData存在的情况需要特殊处理 ,这种说明也没有缓存--直接改变item的值
          this.handleDataList(item, code, level, status, this.data.oldData)
        } else {
          item.status = status; // 根据父级选中状态更新子级状态
        }
        // item.status = status; // 根据父级选中状态更新子级状态22.2.28
        if (level === '1' && item.level === '3') item.isarea = true; //  如果当前获取的是直辖市的下级数据，添加一个区的标记
        if (item.status) {
          // 不知道为啥这里会push，但是要判断是否重复✨
          const ind = checkedList.findIndex((region) => {
            return region.code === item.code;
          });
          ind < 0 && checkedList.push(item);
          ind > -1 && (checkedList[ind].status = item.status);
        }
        return item;
      });

      if (level === '1') cityObj[code] = dataList; // 将该省级的市级数据存入缓存
      if (level === '2') areaObj[code] = dataList; // 将该市的区级数据存入缓存

      // 如果是第一次调用，则是获取的省级，取省级数据的第一项再调一次，获取市级
      if (!level) {
        regionObj[temp][0].active = true;
        this.getRegion(regionObj[temp][0].code, regionObj[temp][0].level);
      }
      this.setData({ regionObj, cityObj, areaObj })
    },
    //设置选中项
    setCheckedList(item) {
      let { checkedList } = this.data;
      const index = checkedList.findIndex((region) => {
        return region.code === item.code;
      })
      if (item.status !== '') {
        if (index < 0) {
          checkedList.push(item);
        } else {
          checkedList[index].status = item.status;
        }
      } else {
        if (index > -1) checkedList.splice(index, 1);
      }
      this.setData({ checkedList })
    },
    clearChildrenCheckedLsit(parentCode, status) { //特殊情况
      if (status) return;
      let { checkedList } = this.data;
      let uncheckedList = checkedList.filter(item => item.parent == parentCode)
      checkedList = checkedList.filter(item => item.parent != parentCode)
      if (uncheckedList.length) {
        uncheckedList.forEach(item => {
          checkedList = checkedList.filter(i => i.parent != item.code)
        })
      }
      this.setData({ checkedList })
    },
    // 选中事件(status:选中状态，分别是：'checked':选中/'semi-checked':半选中/'':未选中)
    checkeEvent(e) {
      let { code, level, status, active, isarea } = e.currentTarget.dataset; // isarea：当市级的level为3时传入
      let { regionObj, cityObj, areaObj, provinceSingle } = this.data;
      if (provinceSingle) { //是否是单选
        regionObj.provinceList = regionObj.provinceList.map(region => {
          region.status = '';
          return region;
        })
        cityObj = this.resetStatus(cityObj);
        areaObj = this.resetStatus(areaObj);
        this.setData({ cityObj, areaObj })
      }
      if (level === '1' && provinceSingle) {
        this.reset()
      }
      /*-不是单选的情况-*/

      let key = level === '1' ? 'provinceList' :
        level === '2' ? 'curCityList' :
          level === '3' && isarea ? 'curCityList' : 'curAreaList';

      let obj = level === '1' ? cityObj : level === '2' ? areaObj : {};

      status = status ? '' : 'checked'; // 切换当前选中状态

      // 更新当前点击节点的状态
      regionObj[key] = regionObj[key].map(item => {
        if (item.code === code) {
          item.status = status;
          this.setCheckedList(item); // 设置选中项数据
        }
        return item;
      })
      // 如果当前选中的不是区级则修改活动状态
      // 如果对应的市级列表/区级列表已经存在缓存
      if (obj[code]) {
        obj[code] = obj[code].map(item => {
          item.status = status;
          this.setCheckedList(item); // 设置选中项数据
          // 如果是省级触发选中事件，将区级状态同步更新
          if (level === '1') {
            if (areaObj[item.code]) {
              areaObj[item.code] = areaObj[item.code].map(area => {
                area.status = status;
                this.setCheckedList(area); // 设置选中项数据
                return area;
              })
            } else {
              // 22.4.28添加-这断代码
              this.clearChildrenCheckedLsit(item.code, item.status)
              // 
            }
          }
          return item;
        })
      }
      // 如果是反向一开始并没有缓存，但是checklist已经有了(孩子节点) 所有也要清除掉 
      this.clearChildrenCheckedLsit(code, status)
      !(level === '3' && !isarea) && this.getChildData({ code, level, active, status });
      this.setData({ regionObj, cityObj, areaObj });
    }
  }
})
