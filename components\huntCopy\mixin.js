import {
  clone
} from '../../utils/util';
import dayjs from 'dayjs';
const app = getApp();
/*---------常量------------*/
// 总的搜索列表
let dateTagList = []; // 日期tag列表
const bolAry = [{
    id: 'true',
    name: '有'
  },
  {
    id: 'false',
    name: '无'
  }
]
// 时间戳转日期
function transformDate(num) {
  return dayjs(num).format('YYYY-MM-DD');
}
// 计算日期
function calcDate(start, end) {
  const now = dayjs();
  const oneYearAgoStart = now.subtract(end, 'year');
  const oneYearAgoEnd = now.subtract(start, 'year');

  const startDateStr = end ? transformDate(oneYearAgoStart) : '';
  const endDateStr = transformDate(oneYearAgoEnd);

  const dateCombination = `${startDateStr}$${endDateStr}`;
  dateTagList.push(dateCombination);
  return dateCombination;
}
// 22.10.3ps目前所属产业 以及所属产业多选（最多选五条产业链）-折叠

let searchLeftLists = [{
    title: '基础筛选',
    map: {
      ent_name: '企业名称',
      trade_types: '所属行业',
      areas: '所在地区'
    }
  },
  {
    title: '招商优选',
    map: {
      head_ent_flag: '龙头企业', //22.10.30新
      ent_expand: '疑似扩张', //22.10.30新
      ent_size: '企业规模',
      benefits_assess: '效益评估',
      technology: '科技型企业'
    }
  },
  {
    title: '企业相关',
    map: {
      est_date: '注册时间',
      reg_capital: '注册资本',
      ent_status_list: '企业状态',
      enterprise_license_list: '企业许可',
      enterprise_license_list: '企业类型',
      ent_entity_type: '实体类型' //22.10.30新
    }
  },
  {
    title: '经营状态',
    map: {
      mobile_phone_flag: '联系方式',
      fixed_phone_flag: '手机号码',
      email_flag: '联系邮箱',
      // "": "实缴资本",
      insured_num: '从业人数',
      financing_info_list: '融资信息',
      listing_status: '上市状态', //22.10.30
      patent_info: [], //专利信息
      tendering_and_bidding: '招投标',
      job_flag: '招聘',
      // "": "建筑资质",
      // "": "资质证书",
      tax_credit_flag: '纳税信用'
      // "": "进出口信用",
      // "land": "国有土地受让"
    }
  },
  {
    title: '知识产权',
    map: {
      trade_mark_info_flag: '商标信息',
      copyrights_flag: '软件著作权',
      copyright_of_works: '作品著作权',
      official_website_info: '官网信息',
      icp_filing: 'ICP备案',
      android_app: '安卓APP',
      apple_app: '苹果APP',
      applet: '小程序',
      we_chat_official_account: '微信公众号',
      weibo_flag: '微博'
    }
  },
  {
    title: '风险信息',
    map: {
      dishonest_info: '失信信息',
      // "": "破产重整",
      judgment_doc: '裁判文书',
      administrative_penalty: '行政处罚',
      // "": "清算信息",
      // "": "环保处罚",
      chattel_mortgage: '动产抵押',
      business_abnormalities: '经营异常'
      // "": "股权冻结"
    }
  }
];
let searchTermList = [{
    title: '所在地区',
    type: 'areas',
    special: 'pop',
    content: ''
  },
  {
    title: '所属行业',
    type: 'trade_types',
    special: 'pop',
    content: ''
  },
  {
    title: '企业规模',
    type: 'ent_size',
    icon: true,
    list: [{
        id: 'S',
        name: 'S'
      },
      {
        name: 'A+',
        id: 'APLUS'
      },
      {
        id: 'A',
        name: 'A'
      },
      {
        name: 'B+',
        id: 'BPLUS'
      },
      {
        id: 'C+',
        name: 'C'
      },
      {
        id: 'D',
        name: 'D'
      },
      {
        name: 'E+',
        id: 'EPLUS'
      },
      {
        id: 'E',
        name: 'E'
      }
    ]
  },
  {
    title: '科技型企业',
    type: 'technology',
    isOpenIcon: true,
    isOpen: true,
    list: [{
        id: 'HN',
        name: '高新技术企业'
      },
      {
        id: 'MST',
        name: '科技型中小企业'
      },
      {
        id: 'G',
        name: '瞪羚企业'
      },
      {
        id: 'SME',
        name: '专精特新-专精特新中小企业'
      },
      {
        id: 'CMI',
        name: '专精特新-制造业单项冠军'
      },
      {
        id: 'SG',
        name: '专精特新-专精特新小巨人'
      },
      {
        id: 'U',
        name: '独角兽企业'
      },
      {
        id: 'GT',
        name: '科技小巨人'
      },
    ]
  },
  {
    title: '注册年限',
    type: 'est_date',
    min: '最低年限',
    max: '最高年限',
    unit: '年',
    genre: 'tpop', //区分类型
    list: [{
        id: calcDate(0, 1),
        name: '1年内'
      },
      {
        id: calcDate(1, 2),
        name: '1-2年'
      },
      {
        id: calcDate(2, 3),
        name: '2-3年'
      },
      {
        id: calcDate(3, 5),
        name: '3-5年'
      },
      {
        id: calcDate(5, 0),
        name: '5年以上'
      }
    ]
  },
  {
    title: '注册资本',
    type: 'reg_capital',
    min: '最低资本',
    max: '最高资本',
    unit: '万元',
    genre: 'input', //区分类型
    list: [{
        id: '0$100',
        name: '100万以下'
      },
      {
        id: '100$200',
        name: '100-200万'
      },
      {
        id: '500$1000',
        name: '500-1000万'
      },
      {
        id: '1000$5000',
        name: '1000-5000万'
      },
      {
        id: '5000$',
        name: '5000万以上'
      }
    ]
  },
  {
    title: '企业状态', //企业经营状态
    type: 'ent_status_list',
    list: [{
        id: '1',
        name: '在营'
      },
      {
        id: '2',
        name: '吊销'
      },
      {
        id: '3',
        name: '注销'
      },
      {
        id: '9',
        name: '其他'
      }
    ]
  },
  {
    title: '实体类型',
    type: 'ent_entity_type',
    list: [{
        id: '1',
        name: '工商'
      },
      {
        id: '3',
        name: '个体'
      }
    ]
  },
  {
    title: '企业类型',
    type: 'enterprise_license_list',
    special: 'pop',
    content: ''
  },
  {
    title: '企业许可',
    type: 'enterprise_license_list',
    special: 'pop',
    content: ''
  },
  {
    title: '手机号码',
    type: 'mobile_phone_flag', //到时候这个需要和下面那个合并成 fixed_phone_flag
    list: [...bolAry]
  },
  {
    title: '固定电话',
    type: 'fixed_phone_flag',
    list: [...bolAry]
  },
  {
    title: '企业名称',
    type: 'ent_name', //到时候这个需要和下面那个合并成 fixed_phone_flag
    special: 'input',
    content: ''
  },
  {
    title: '联系邮箱',
    type: 'email_flag',
    list: [...bolAry]
  },
  {
    title: '从业人数',
    type: 'insured_num',
    list: [{
        id: '0$49',
        name: '0-49'
      },
      {
        id: '50$99',
        name: '50-99'
      },
      {
        id: '100$499',
        name: '100-499'
      },
      {
        id: '500$999',
        name: '500-999'
      },
      {
        id: '1000$4999',
        name: '1000-4999'
      },
      {
        id: '5000$',
        name: '5000人以上'
      }
    ]
  },
  {
    title: '融资信息',
    type: 'financing_info_list',
    isOpen: true,
    isOpenIcon: true,
    list: [{
        id: 'AW',
        name: '天使轮'
      },
      {
        id: 'A',
        name: 'A轮'
      },
      {
        id: 'B',
        name: 'B轮'
      },
      {
        id: 'C',
        name: 'C轮'
      },
      {
        id: 'D',
        name: 'D轮'
      },
      {
        id: 'EPLUS',
        name: 'E轮及以上'
      },
      {
        id: 'IPO',
        name: 'IPO'
      },
      {
        id: 'F',
        name: '定向增发'
      },
    ]
  },
  {
    title: '招投标',
    type: 'tendering_and_bidding',
    list: [...bolAry]
  },
  {
    title: '招聘',
    type: 'job_flag',
    list: [...bolAry]
  },
  {
    title: '纳税信用',
    type: 'tax_credit_flag',
    list: [{
        id: 'true',
        name: 'A级'
      },
      {
        id: 'false',
        name: '非A级'
      }
    ]
  },
  {
    title: '商标信息',
    type: 'trade_mark_info_flag',
    isParent: true,
    list: [...bolAry]
  },
  {
    title: '安卓APP',
    type: 'android_app',
    isParent: true,
    list: [...bolAry]
  },
  {
    title: '苹果APP',
    type: 'apple_app',
    isParent: true,
    list: [...bolAry]
  },
  {
    title: '小程序',
    type: 'applet',
    isParent: true,
    list: [...bolAry]
  },
  {
    title: '微信公众号',
    type: 'we_chat_official_account',
    isParent: true,
    list: [...bolAry]
  },
  {
    title: '微博',
    type: 'weibo_flag',
    isParent: true,
    list: [...bolAry]
  },
  {
    title: '失信信息',
    type: 'dishonest_info',
    list: [...bolAry]
  },
  {
    title: '裁判文书',
    type: 'judgment_doc',
    list: [...bolAry]
  },
  {
    title: '行政处罚',
    type: 'administrative_penalty',
    list: [...bolAry]
  },
  {
    title: '动产抵押',
    type: 'chattel_mortgage',
    list: [...bolAry]
  },
  {
    title: '经营异常',
    type: 'business_abnormalities',
    list: [...bolAry]
  },
  {
    title: '软件著作权',
    type: 'copyrights_flag',
    list: [...bolAry]
  },
  {
    title: '作品著作权',
    type: 'copyright_of_works',
    list: [...bolAry]
  },
  {
    title: '官网信息',
    type: 'official_website_info',
    list: [...bolAry]
  },
  {
    title: 'ICP备案',
    type: 'icp_filing',
    list: [...bolAry]
  },

  {
    title: '龙头企业',
    type: 'head_ent_flag',
    list: [{
        id: 'true',
        name: '是龙头企业'
      },
      {
        id: 'false',
        name: '非龙头企业'
      }
    ]
  },
  {
    title: '疑似扩张',
    type: 'ent_expand',
    list: [{
        id: 'A',
        name: '近期融资'
      },
      {
        id: 'B',
        name: ' 近期新增分支机构'
      },
      {
        id: 'C',
        name: '近期疑似扩张'
      },
      {
        id: 'D',
        name: '近期有对外投资'
      },
      {
        id: 'E',
        name: '近期新增专利公示'
      },
      {
        id: 'F',
        name: '近期新增专利申请'
      }
    ]
  },
  {
    title: '上市状态',
    type: 'listing_status',
    list: [{
        id: 'A',
        name: 'A股'
      },
      {
        id: 'B',
        name: 'B股'
      },
      {
        id: 'NTB',
        name: '新三板'
      },
      {
        id: 'HK',
        name: '港股'
      },
      {
        id: 'STAR',
        name: '科创板'
      },
      {
        id: 'USA',
        name: '美股'
      },
      {
        id: 'NONE',
        name: '非上市'
      }
    ]
  },
  {
    title: '专利信息',
    vip: true,
    type: 'patent_info',
    list: [{
        id: '4',
        name: '发明授权'
      },
      {
        id: '1',
        name: '发明公告'
      },
      {
        id: '2',
        name: '实用新型'
      },
      {
        id: '3',
        name: '外观设计'
      }
    ]
  },
  {
    title: '效益评估',
    type: 'benefits_assess',
    list: [{
        id: 'S',
        name: 'S'
      },
      {
        name: 'A+',
        id: 'APLUS'
      },
      {
        id: 'A',
        name: 'A'
      },
      {
        name: 'B+',
        id: 'BPLUS'
      },
      {
        id: 'C+',
        name: 'C'
      },
      {
        id: 'D',
        name: 'D'
      },
      {
        name: 'E+',
        id: 'EPLUS'
      },
      {
        id: 'E',
        name: 'E'
      }
    ]
  }
];

// params最后会划分到下面
let params = {
  ent_name: '', //新，企业名称
  area_code_list: [], //地区,
  industry_code_list: [], //行業
  technology: [], //科技型企业
  listing_status: [], //上市状态
  ent_expand: [], //疑似扩张
  ent_size: [], // 企业规模
  benefits_assess: [], // 效益评估
  // capital_event: '', // 	资本事件-这个目前没用到--放开后搜索模版哪里需要单独处理
  est_date: [], //	注册时间
  reg_capital: [], // 注册资本
  ent_status_list: [], //企业状态
  ent_entity_type: [], //实体类型
  enterprise_license_list: [], //企業类型
  all_cert_data: [], //企业许可
  fixed_phone_flag: [], // 联系方式
  mobile_phone_flag: [], //联系手机号码
  email_flag: [], //新，联系邮箱
  insured_num: [], //新，参保人数
  tendering_and_bidding: [], //新，招投标
  job_flag: [], //新，招聘
  tax_credit_flag: [], //新， 纳税信用
  financing_info_list: [], //新，融资信息
  trade_mark_info_flag: [], //新， 商标信息
  super_dimension_patent: [], //新， 专利信息
  android_app: [], //新， 安卓APP
  apple_app: [], //新， 苹果APP
  applet: [], //新， 小程序
  we_chat_official_account: [], //新， 微信公众号
  weibo_flag: [], //新， 微博
  dishonest_info: [], //新， 失信信息
  judgment_doc: [], //新， 裁判文书
  administrative_penalty: [], //新， 行政处罚
  chattel_mortgage: [], //新， 行政处罚
  business_abnormalities: [], //新， 行政处罚
  copyrights_flag: [], //新， 软件著作权
  copyright_of_works: [], //新， 作品著作权
  official_website_info: [], //新， 官网信息
  icp_filing: [], //新， ICP备案
  industrial_list: [], //新兴产业
  head_ent_flag: [] //龙头企业
};
// 搜索--单选的情况
let searRadioConstant = {
  list: [
    'mobile_phone_flag',
    'fixed_phone_flag',
    'email_flag',
    'tendering_and_bidding',
    'tax_credit_flag',
    'trade_mark_info_flag',
    'android_app',
    'apple_app',
    'applet',
    'we_chat_official_account',
    'dishonest_info',
    'judgment_doc',
    'administrative_penalty',
    'chattel_mortgage',
    'business_abnormalities',
    'copyrights_flag',
    'copyright_of_works',
    'official_website_info',
    'icp_filing',
    'weibo_flag',
    'super_dimension_patent',
    'job_flag',
    'head_ent_flag'
  ],
  map: {
    mobile_phone_flag: '联系方式',
    fixed_phone_flag: '手机号码',
    email_flag: '联系邮箱',
    tendering_and_bidding: '招投标',
    job_flag: '招聘',
    tax_credit_flag: '纳税信用',
    trade_mark_info_flag: '商标信息',
    android_app: '安卓APP',
    apple_app: '苹果APP',
    applet: '小程序',
    we_chat_official_account: '微信公众号',
    weibo_flag: '微博',
    dishonest_info: '失信信息',
    judgment_doc: '裁判文书',
    administrative_penalty: '行政处罚',
    chattel_mortgage: '动产抵押',
    chattel_mortgage: '经营异常',
    copyrights_flag: '软件著作权',
    copyright_of_works: '作品著作权',
    official_website_info: '官网信息',
    icp_filing: 'ICP备案',
    business_abnormalities: '经营异常',
    head_ent_flag: '龙头企业'
  }
};
// 搜索--多选的情况
let searMultiSelectConstant = {
  list: [
    'capital_event',
    'ent_entity_type',
    'ent_status_list',
    'technology',
    'financing_info_list',
    'listing_status',
    'patent_info',
    'ent_expand',
    'ent_size',
    'benefits_assess',
    'insured_num'
  ],
  map: {
    capital_event: '资本事件',
    ent_entity_type: '实体类型',
    ent_status_list: '企业状态',
    technology: '科技型企业',
    financing_info_list: '融资信息',
    listing_status: '上市状态',
    patent_info: '专利信息',
    ent_expand: '疑似扩张',
    ent_size: '企业规模',
    benefits_assess: '效益评估',
    insured_num: '从业人数'
  }
};
let searCheckPopConstant = {
  //多选+自定义时间弹窗类型
  list: ['est_date'],
  map: {
    est_date: '注册时间'
  },
  tip: {
    est_date: {
      max: '最高年限',
      min: '最低年限',
      unit: '年'
    }
  }
};
let searMultIptConstant = {
  //多选+自定义时间弹窗类型
  list: ['reg_capital'],
  map: {
    reg_capital: '注册资本',
  },
  tip: {
    reg_capital: {
      max: '最高资本',
      min: '最低资本',
      unit: '万元'
    },
  }
};
let searMultAllConstant = {
  list: [...searCheckPopConstant['list'], ...searMultIptConstant['list']]
};
// 搜索--弹窗的情况
let searMultPopConstant = {
  areas: {
    data: 'area_code_list',
    label: '所在地区'
  },
  trade_types: {
    data: 'industry_code_list',
    label: '所属行业'
  },
  enterprise_license_list: {
    data: 'enterprise_license_list',
    label: '企业类型'
  },
  enterprise_license_list: {
    data: 'all_cert_data',
    label: '企业许可'
  },
  industrial_list: {
    data: 'industrial_list_data',
    label: '新兴产业'
  }
};

// 搜索--输入框
let searInputConstant = {
  list: ['ent_name'],
  map: {
    ent_name: '企业名称'
  }
};
// a.1 处理上传布尔的情况 --不是数组要拿出来
let boolConstant = [
  'tendering_and_bidding',
  'job_flag',
  'trade_mark_info_flag',
  'android_app',
  'apple_app',
  'applet',
  'we_chat_official_account',
  'weibo_flag',
  'official_website_info',
  'icp_filing',
  'head_ent_flag'
];
// a.2 处理上传对象-有父亲的情况
let parentType = ['super_dimension_'];
// 根据上面获取对应的信息
let renderList = (function (left, allList) {
  let arr = [];
  left.forEach(item => {
    arr.push({
      title: item.title,
      onlyText: true,
      isOpen: true,
      isActive: false, //这是有map决定的
      map: Object.keys(item.map).map(i => {
        return {
          key: i,
          value: item.map[i],
          active: false
        };
      })
    });
    Object.keys(item['map']).forEach(itm => {
      allList.forEach(i => {
        if (itm == i.type) {
          arr.push(i);
        }
      });
    });
  });
  return arr;
})(searchLeftLists, searchTermList);

/*---------方法------------*/
// 校验自定义输入的情况
let checkoutSear = function (paramsData = {}) {
  let list = [...searCheckPopConstant['list'], ...searMultIptConstant['list']];
  let bool = Object.keys(paramsData).some(i => {
    if (list.includes(i) && paramsData[i].length == 1) {
      //只考虑自定义 ，所以数组只会有一个
      if (searCheckPopConstant['list'].includes(i)) {
        let minDate = paramsData[i][0]?.start;
        let maxDate = paramsData[i][0]?.end;
        let startTime = new Date(minDate).getTime();
        let endTime = new Date(maxDate).getTime();
        if (startTime >= endTime) {
          wx.showToast({
            title: `${searCheckPopConstant['tip'][i]['min']}不能大于等于${searCheckPopConstant['tip'][i]['max']}`,
            icon: 'none'
          });
          return true;
        }
      } else if (searMultIptConstant['list'].includes(i)) {
        if (
          parseFloat(paramsData[i][0]?.start) >=
          parseFloat(paramsData[i][0]?.end)
        ) {
          wx.showToast({
            title: `${searMultIptConstant['tip'][i]['min']}不能大于等于${searMultIptConstant['tip'][i]['max']}`,
            icon: 'none'
          });
          return true;
        }
      }
    }
  });
  return !bool;
};
// 调用清空
let clearChildComponent = function (that, idName = '#hunt') {
  let child = that.selectComponent(idName);
  return function () {
    if (child) {
      child.clearSear();
    } else {
      child = that.selectComponent(idName);
      console.log(child);
      child.clearSear();
    }
  };
  // ps：使用方法 clearChildComponent(this)()
};
// 调用回填
let fillChildComponent = function (that, idName = '#hunt') {
  let child = that.selectComponent(idName);
  return function (tempObj) {
    if (child) {
      child.setBackfillData(tempObj);
    } else {
      child = that.selectComponent(idName);
      child.setBackfillData(tempObj);
    }
  };
  // ps：使用方法 fillChildComponent(this)(tempObj)
};
// 处理多选地区 有父级时，孩子不需要的情况
function handleMultiple(ary = []) {
  if (!ary.length) return [];
  let temParr = JSON.parse(JSON.stringify(ary));
  let checkedAry = temParr.filter(i => i.status == 'checked' && !i.ischildren);
  checkedAry = checkedAry.filter(
    item => !checkedAry.map(item => item.code).includes(item.parent)
  );
  if (!checkedAry.length) return ary;
  checkedAry.forEach(i => {
    temParr = temParr.filter(itm => i.code != itm.parent);
  });
  return temParr;
}
// 处理多选数据 --后面这里还需要写一个类似的方法 这种处理后的数据只能用于查询 不用作为回选
let handleData = function (params) {
  let obj = clone(params); //Object.assign
  // 处理对选相关数据
  obj['areas'] =
    params.area_code_list?.length > 0 ?
    handleMultiple(params.area_code_list)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  obj['trade_types'] =
    params.industry_code_list?.length > 0 ?
    handleMultiple(params.industry_code_list)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  obj['enterprise_license_list'] =
    params.enterprise_license_list?.length > 0 ?
    handleMultiple(params.enterprise_license_list)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  obj['enterprise_license_list'] =
    params.all_cert_data?.length > 0 ?
    handleMultiple(params.all_cert_data)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  // 这个比较特殊
  obj['industrial_list'] =
    params.industrial_list_data?.length > 0 ?
    params.industrial_list_data.filter(i => i.active === true).map(i => i.code) : [];

  return JSON.stringify(obj);
};
// 关闭企业名称弹窗
let closeEntNamePop = function (that, idName = '#hunt') {
  let child = that.selectComponent(idName);
  return function () {
    if (child) {
      child.closeInptPop();
    } else {
      child = that.selectComponent(idName);
      child?.closeInptPop();
    }
  };
};
// 处理高级搜索类似地区弹窗---名字哪里
let getNameFromPop = function (data) {
  let arr = [];

  data?.length > 0 &&
    data.forEach((i, idx, oldArr) => {
      if (i.status == 'checked') {
        if (i.parent && i.parent != '') {
          // 有父亲
          let parent = oldArr.filter(itm => i.parent == itm.code)[0];
          parent.status == 'checked' ? arr.push(parent) : arr.push(i);
        } else {
          // 没有父亲
          arr.push(i);
        }
      }
    });
  // 排重
  let newobj = {};
  arr = arr.reduce((preVal, curVal) => {
    newobj[curVal.code] ? '' : (newobj[curVal.code] = preVal.push(curVal));
    return preVal;
  }, []); //.sort((a, b) => (a.level - b.level))
  return arr.map(item => item.name).join('');
};
// 将'true','false'转换成==》true false 如果有前缀还要整合成对象形式
let handlestructure = function (data) {
  let params = Object.assign({}, data);
  boolConstant.forEach(item => {
    if (params[item]?.length) {
      params[item] = JSON.parse(params[item][0]); //将 "true"==>true
    } else {
      delete params[item];
    }
  });
  parentType.forEach(i => {
    Object.keys(params).forEach(itm => {
      if (itm.startsWith(i)) {
        let suffix = itm.slice(i.length),
          prefix = itm.slice(0, i.length - 1),
          obj = {};
        obj[suffix] = params[itm];
        params[prefix] = {
          ...params[prefix],
          ...obj
        };
        delete params[itm];
      }
    });
  });
  return params;
};

module.exports = {
  // 企业搜索常量
  dateTagList,
  renderList,
  params,
  searRadioConstant,
  searMultiSelectConstant,
  searMultAllConstant,
  searMultPopConstant,
  searInputConstant,
  searCheckPopConstant,
  searMultIptConstant,
  searchTermList,
  // 方法
  checkoutSear,
  clearChildComponent,
  handleData,
  fillChildComponent,
  closeEntNamePop,
  getNameFromPop,
  handlestructure
};