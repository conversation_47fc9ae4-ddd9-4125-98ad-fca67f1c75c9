// companyPackage/pages/searchBusiness/searchBusiness.js
import { business, home } from "../../../service/api";
import { debounce, formatDate } from "../../../utils/formate"
import { getHeight } from '../../../utils/height';
const enumType = {
  LISTED: '榜单',
  CHAIN: '产业链',
  ENTERPRISE: '企业'
}
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isLogin: false,
    ents_name: "",
    inputShowed: false, // 是否聚焦
    ent_name: "", // 搜索值
    historyList: [], // 搜索历史
    browsingHistory: [], // 浏览历史
    timer: null,
    // 弹窗相关 
    popType: '',
    showVisible: false, // 是否显示弹窗 
    title: '',
    content: '',
    cancelBtnText: "取消",
    confirmBtnText: "删除",
    // 浏览历史高度 
    scrollHeight: 'auto',
    filtrateHeight: 'auto',
    statusBarHeight: 'auto',
    keywordList: [], // 关键词数组
    industrialList: [], // 产业链数组
    showIndustrialList: [], // 产业链用作展示
    punditNum: [], // 权威榜单数量
    vipVisible: false, // vip弹窗
  },
  // 输入框键入内容
  onInput: debounce(function ([...e]) {
    let keyword = e[0].detail.value;
    this.setData({ ent_name: keyword });
    if (keyword || keyword !== '') {
      this.inputQuest();
      this.getChainList({ keyword, search_chain: '1' });
    }/*  else {
      this.setData({ ent_name: '', ents_name: '', keywordList: [], industrialList: [] })
    } */
  }),
  // 搜索关键字
  inputQuest() {
    const { ent_name } = this.data;
    if (ent_name) {
      app.showLoading('加载中')
      return business.search({ keyword: ent_name }).then(({ dataList = [] }) => {
        const getKeyWord = this.onHandlerKewWord(dataList, "tagName");
        this.setData({ keywordList: getKeyWord });
      }).finally(() => {
        wx.hideLoading()
      })
    }
  },
  // 输入框搜索事件
  onConfirm({ detail: { value: tag_name } = {} } = {}) {
    console.log(tag_name);
    let { ent_name, isLogin } = this.data;
    let name = tag_name || ent_name;
    if (name) {
      /* &&chain_code=${chain_code} */
      const url = `/companyPackage/pages/industryChain/chainListNew/chainList?key_word=${name}`;
      app.route(this, url, 'navigateTo');
      isLogin && home.addHistory({ keyword: name, model_type: 'BUSINESS' });
    }
  },
  // 获取搜索历史记录
  getSearchHisList() {
    return home.getHistory('BUSINESS').then((data) => {
      this.setData({ historyList: data });
    })
  },
  // 获取浏览历史记录
  getBevHisList() {
    return home.getBevHis('BUSINESS').then(data => {
      data = data.map(items => {
        const { update_time, second_model_type } = items;
        items.second_model_type_text = enumType[second_model_type];
        return { ...items, update_time: formatDate(update_time, "yyyy-MM-dd") };
      })
      this.setData({ browsingHistory: data });
    })
  },
  // 获取相关产业链
  getChainList(params) {
    return business.queryIndustryChainList(params).then(({ chains_objs = [], zsTop } = {}) => {
      const industrialList = this.onHandlerKewWord(chains_objs, "name");
      this.setData({ industrialList });
      this.setData({
        punditNum: zsTop,
        showIndustrialList: industrialList.length > 3 ? industrialList.slice(0, 3) : industrialList
      })
    })
  },
  // 关键词处理
  onHandlerKewWord(list, key) {
    const { ent_name } = this.data;
    return list.reduce((result, items) => {
      const _tagName = items[key].replace(new RegExp(`(${ent_name})`), "*=*$1*=*");
      const split = _tagName.split("*=*");
      const handler = split.map(word => {
        if (word === ent_name) return { text: word, type: 'HIGH' }
        return { text: word, type: 'DEFAULT' }
      });
      return [...result, { ...items, label: handler }];
    }, []);
  },
  // 点击删除图标 
  handleIcon(e) {
    const type = e.currentTarget.dataset['index']
    const that = this;
    switch (type) {
      // 情况最近搜索
      case 'a':
        wx.showModal({
          title: '删除搜索',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              home.clearHistory('BUSINESS').then(() => {
                that.setData({
                  showVisible: false,
                  historyList: []
                })
                app.showToast("删除成功");
              });
            }
          }
        })
        break;
      case 'b': //清空浏览历史
        wx.showModal({
          title: '删除浏览历史',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              // 发送请求 --成功删除历史
              home.detBevHis('BUSINESS').then(() => {
                app.showToast('删除成功!')
                that.setData({ browsingHistory: [] })
              })
            }
          }
        })
        break;
      default:
        break;
    }
  },
  // 点击关键词标签
  onClickTag(e) {
    console.log(e, 333);
    const { tagName } = e.currentTarget.dataset['item'];
    this.onConfirm({ detail: { value: tagName } });
  },
  // 点击搜索历史
  historyTap(e) {
    const { key_word } = e.target.dataset['item']
    this.onConfirm({ detail: { value: key_word } })
  },
  // 查看全部产业链
  handleClickViewAllChain() {
    const { ent_name } = this.data;
    const url = `/companyPackage/pages/industryChain/chainSearch/chainSearch?key_word=${ent_name}&second_model_type=CHAIN`;
    app.route(this, url);
  },
  // 点击产业链
  onIndustrClick({ currentTarget: { dataset: { item } } }) {
    const { isLogin } = this.data;
    if (isLogin) {
      home.addBevHis({
        enterprise_name: item.name,
        enterprise_id: item.code,
        behavior_history_mode: 'INDEX_PAGE',
        enterprise_log: "-",
        model_type: 'BUSINESS',
        second_model_type: 'CHAIN'
      }) //新增浏览历史 
    }
    const url = `/companyPackage/pages/industryChain/chainList/chainList?chainName=${item.name}&chain_code=${item.code}`
    app.route(this, url)

  },
  // 获取权威榜单详情，判断是否还有查看次数
  async getDetailData(data) {
    let {
      data_source,
      id,
      type,
      theme_type
    } = data;
    try {
      app.showLoading('加载中...')
      let res1 = await home.getDefinitiveTop({
        data_source,
        id,
        theme_type,
        type,
        page_index: 1, //偏移量
        page_size: 100, //每页多少条
      })
      let arr = res1?.items?.map(item => {
        let {
          tags,
          fa_ren,
          reg_cap,
          es_date,
          ent_name,
          ent_logo,
          collected,
          ent_id,
          location,
          region,
          official_website
        } = item
        tags = tags.filter((tag, ind) => ind < 3);
        return {
          tags,
          legal_person: fa_ren,
          reg_capital: reg_cap,
          register_date: es_date,
          ent_name,
          logo: ent_logo,
          collect: collected,
          ent_id,
          location,
          region,
          official_website
        };
      }) || []
      return JSON.stringify(arr)
    } catch (error) {
      if (error.data.code.indexOf('BusinessPackageErrorCode/OUT_LIMIT_REQUEST_TO_VIP') > 0) {
        this.setData({
          vipVisible: true
        })
      }
      wx.hideLoading()
      return
    }
  },
  // 查看全部权威榜单
  handleClickList() {
    const { ent_name } = this.data;
    const url = `/companyPackage/pages/authoritativeList/authoritativeList?keyword=${ent_name}`;
    app.route(this, url);
  },
  // 权威榜单权限判断
  async listedAuth({ extra_param }) {
    const { isLogin } = this.data;
    const { type, id, data_source, name, release_date, source } = JSON.parse(extra_param);
    const tempObj = {
      type,
      id,
      data_source,
      name,
      release_date,
      source,
      theme_type: name
    };
    // 判断是否登录 
    if (!isLogin) {
      app.route(this, '/pages/login/login')
      return
    }
    const obj = JSON.stringify(tempObj);
    const res = await this.getDetailData(tempObj);
    wx.hideLoading();
    // 判断是否有权限继续查看权威榜单详情页面
    return { obj, res }
  },
  async goDetail({ currentTarget: { dataset: { item } } }) {
    let listedInfo = {};
    if (item.second_model_type === 'LISTED') {
      // 权威榜单相关数据
      const { obj, res } = await this.listedAuth(item);
      listedInfo = { obj, res };
      if (!res) return;
    }
    const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${item.enterprise_id}`);
    const tempObj = {
      CHAIN: `/companyPackage/pages/industryChain/chainList/chainList?chainName=${item.enterprise_name}&chain_code=${item.enterprise_id}`,
      LISTED: `/companyPackage/pages/authoritativeDetail/authoritativeDetail?params=${listedInfo.obj}&res=${listedInfo.res}`,
      ENTERPRISE: `/subPackage/pages/webs/index?url=${url}`
    };
    app.route(this, tempObj[item.second_model_type]);
  },
  onClear() {
    this.unLocked()
    this.setData({ ent_name: '', ents_name: '', keywordList: [], industrialList: [] })
  },
  goBack() {
    this.unLocked()
    this.init()
    app.route(this, null, 'navigateBack')
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      loading: false,
      inputShowed: false
    })
  },
  init() {
    this.setData({
      ent_name: "",
      ents_name: '',
      inputShowed: false
    });
  },
  scrollH() {
    var that = this;
    getHeight(that, ['.search-wrapper', '.search_a'], (data) => {
      const { screeHeight, res, statusBarHeight } = data
      let h1 = res[0]?.height || 0
      let h2 = res[1]?.height || 0
      let h4 = res[3]?.height || 0
      // 处理search外剩余的高度 
      let filtrateHeight = screeHeight - h1
      // 浏览历史的滚动高度 
      let scrollHeight = screeHeight - h1 - h2 - h4
      that.setData({
        scrollHeight: scrollHeight,
        filtrateHeight: filtrateHeight,
        statusBarHeight: statusBarHeight,
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const { login } = app.globalData
    this.scrollH()
    this.setData({ isLogin: login }, () => {
      login && app.showLoading('加载中')
      login && Promise.all([this.getSearchHisList(), this.getBevHisList()])
        .then(res => {
          wx.hideLoading()
        }).catch(err => {
          wx.hideLoading()
        })
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
})