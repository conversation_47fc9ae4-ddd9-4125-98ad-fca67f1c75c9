/**
 * 样式变量定义
 * 统一管理颜色、尺寸等样式变量
 */

/* 颜色变量 */
$primary-color: #E72410;
$primary-color-light: rgba(231, 36, 16, 0.1);
$primary-gradient: linear-gradient(90deg, #FFB2AA 0%, #E72410 100%);

$background-color: #F2F2F2;
$background-white: #FFFFFF;

$text-color-primary: #20263A;
$text-color-secondary: #525665;
$text-color-placeholder: #9B9EAC;

$border-color: #eee;
$border-color-light: #f0f0f0;

$tag-background: #F4F4F4;
$tag-background-active: $primary-gradient;

$input-background: #F4F4F4;
$input-background-active: $primary-color-light;

/* 尺寸变量 */
$border-radius-small: 4rpx;
$border-radius-medium: 8rpx;
$border-radius-large: 16rpx;

$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 20rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;

$font-size-xs: 24rpx;
$font-size-sm: 25rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;

$height-input: 56rpx;
$height-item: 88rpx;
$height-title: 92rpx;
$height-menu: 100rpx;

/* 阴影变量 */
$shadow-light: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
$shadow-medium: 8px 0px 8px 2px rgba(204, 204, 204, 0.2);

/* 过渡变量 */
$transition-fast: 0.3s;
$transition-medium: 0.5s;
$transition-slow: 1s;

/* Z-index 变量 */
$z-index-dropdown: 999;
$z-index-modal: 1000;
$z-index-toast: 2000;
