import {
  clone
} from '../../utils/util';
import {
  dateTagList,
  searchLeftLists,
  searchTermList
} from './common/changlian';
const app = getApp();
/*---------常量------------*/
// params最后会划分到下面
let params = {
  ent_name: '', //新，企业名称
  area_code_list: [], //地区,
  industry_code_list: [], //行業
  technology: [], //科技型企业
  listing_status: [], //上市状态
  patent_info: [], //专利信息
  ent_expand: [], //疑似扩张
  ent_size: [], // 企业规模
  benefits_assess: [], // 效益评估
  // capital_event: '', // 	资本事件-这个目前没用到--放开后搜索模版哪里需要单独处理
  est_date: [], //	注册时间
  reg_capital: [], // 注册资本
  ent_status_list: [], //企业状态
  ent_entity_type: [], //实体类型
  ent_type: [], //企業类型
  enterprise_license_list: [], //企业许可
  fixed_phone_flag: [], // 联系方式
  mobile_phone_flag: [], //联系手机号码
  email_flag: [], //新，联系邮箱
  insured_num: [], //新，参保人数
  tendering_and_bidding: [], //新，招投标
  job_flag: [], //新，招聘
  tax_credit_flag: [], //新， 纳税信用
  financing_info_list: [], //新，融资信息
  trade_mark_info_flag: [], //新， 商标信息
  android_app: [], //新， 安卓APP
  apple_app: [], //新， 苹果APP
  applet: [], //新， 小程序
  we_chat_official_account: [], //新， 微信公众号
  weibo_flag: [], //新， 微博
  dishonest_info: [], //新， 失信信息
  judgment_doc: [], //新， 裁判文书
  administrative_penalty: [], //新， 行政处罚
  chattel_mortgage: [], //新， 行政处罚
  business_abnormalities: [], //新， 行政处罚
  copyrights_flag: [], //新， 软件著作权
  copyright_of_works: [], //新， 作品著作权
  official_website_info: [], //新， 官网信息
  icp_filing: [], //新， ICP备案
  industrial_list: [], //新兴产业
  head_ent_flag: [] //龙头企业
};
// 搜索--单选的情况
let searRadioConstant = {
  list: [
    'mobile_phone_flag',
    'fixed_phone_flag',
    'email_flag',
    'tendering_and_bidding',
    'tax_credit_flag',
    'trade_mark_info_flag',
    'android_app',
    'apple_app',
    'applet',
    'we_chat_official_account',
    'dishonest_info',
    'judgment_doc',
    'administrative_penalty',
    'chattel_mortgage',
    'business_abnormalities',
    'copyrights_flag',
    'copyright_of_works',
    'official_website_info',
    'icp_filing',
    'weibo_flag',
    'job_flag',
    'head_ent_flag'
  ],
  map: {
    mobile_phone_flag: '联系方式',
    fixed_phone_flag: '手机号码',
    email_flag: '联系邮箱',
    tendering_and_bidding: '招投标',
    job_flag: '招聘',
    tax_credit_flag: '纳税信用',
    trade_mark_info_flag: '商标信息',
    android_app: '安卓APP',
    apple_app: '苹果APP',
    applet: '小程序',
    we_chat_official_account: '微信公众号',
    weibo_flag: '微博',
    dishonest_info: '失信信息',
    judgment_doc: '裁判文书',
    administrative_penalty: '行政处罚',
    chattel_mortgage: '动产抵押',
    chattel_mortgage: '经营异常',
    copyrights_flag: '软件著作权',
    copyright_of_works: '作品著作权',
    official_website_info: '官网信息',
    icp_filing: 'ICP备案',
    business_abnormalities: '经营异常',
    head_ent_flag: '龙头企业'
  }
};
// 搜索--多选的情况
let searMultiSelectConstant = {
  list: [
    'capital_event',
    'ent_entity_type',
    'ent_status_list',
    'technology',
    'financing_info_list',
    'listing_status',
    'patent_info',
    'ent_expand',
    'ent_size',
    'benefits_assess',
    'insured_num'
  ],
  map: {
    capital_event: '资本事件',
    ent_entity_type: '实体类型',
    ent_status_list: '企业状态',
    technology: '科技型企业',
    financing_info_list: '融资信息',
    listing_status: '上市状态',
    patent_info: '专利信息',
    ent_expand: '疑似扩张',
    ent_size: '企业规模',
    benefits_assess: '效益评估',
    insured_num: '从业人数'
  }
};

let searCheckPopConstant = {
  //多选+自定义时间弹窗类型
  list: ['est_date'],
  map: {
    est_date: '注册时间'
  },
  tip: {
    est_date: {
      max: '最高年限',
      min: '最低年限',
      unit: '年'
    }
  }
};
let searMultIptConstant = {
  //多选+自定义时间弹窗类型
  list: ['reg_capital'],
  map: {
    reg_capital: '注册资本',
  },
  tip: {
    reg_capital: {
      max: '最高资本',
      min: '最低资本',
      unit: '万元'
    }
  }
};
let searMultAllConstant = {
  list: [...searCheckPopConstant['list'], ...searMultIptConstant['list']]
};
// 搜索--弹窗的情况
let searMultPopConstant = {
  areas: {
    data: 'area_code_list',
    label: '所在地区'
  },
  trade_types: {
    data: 'industry_code_list',
    label: '所属行业'
  },
  ent_type: {
    data: 'ent_type',
    label: '企业类型'
  },
  enterprise_license_list: {
    data: 'enterprise_license_list',
    label: '企业许可'
  },
  industrial_list: {
    data: 'industrial_list_data',
    label: '新兴产业'
  }
};

// 搜索--输入框
let searInputConstant = {
  list: ['ent_name'],
  map: {
    ent_name: '企业名称'
  }
};
// a.1 处理上传布尔的情况 --不是数组要拿出来
let boolConstant = [
  'tendering_and_bidding',
  'job_flag',
  'trade_mark_info_flag',
  'android_app',
  'apple_app',
  'applet',
  'we_chat_official_account',
  'weibo_flag',
  'official_website_info',
  'icp_filing',
  'head_ent_flag'
];
// a.2 处理上传对象-有父亲的情况
let parentType = ['super_dimension_'];
// 根据上面获取对应的信息
let renderList = (function (left, allList) {
  // console.log(1)
  let arr = [];
  left.forEach(item => {
    arr.push({
      title: item.title,
      onlyText: true,
      isOpen: true,
      isActive: false, //这是有map决定的
      map: Object.keys(item.map).map(i => {
        return {
          key: i,
          value: item.map[i],
          active: false
        };
      })
    });
    Object.keys(item['map']).forEach(itm => {
      allList.forEach(i => {
        if (itm == i.type) {
          arr.push(i);
        }
      });
    });
  });
  return arr;
})(searchLeftLists, searchTermList);

/*---------方法------------*/
// 校验自定义输入的情况
let checkoutSear = function (paramsData = {}) {
  let list = [...searCheckPopConstant['list'], ...searMultIptConstant['list']];
  let bool = Object.keys(paramsData).some(i => {
    if (list.includes(i) && paramsData[i].length == 1) {
      //只考虑自定义 ，所以数组只会有一个
      if (searCheckPopConstant['list'].includes(i)) {
        let minDate = paramsData[i][0]?.start;
        let maxDate = paramsData[i][0]?.end;
        let startTime = new Date(minDate).getTime();
        let endTime = new Date(maxDate).getTime();
        if (startTime >= endTime) {
          wx.showToast({
            title: `${searCheckPopConstant['tip'][i]['min']}不能大于等于${searCheckPopConstant['tip'][i]['max']}`,
            icon: 'none'
          });
          return true;
        }
      } else if (searMultIptConstant['list'].includes(i)) {
        if (
          parseFloat(paramsData[i][0]?.start) >=
          parseFloat(paramsData[i][0]?.end)
        ) {
          wx.showToast({
            title: `${searMultIptConstant['tip'][i]['min']}不能大于等于${searMultIptConstant['tip'][i]['max']}`,
            icon: 'none'
          });
          return true;
        }
      }
    }
  });
  return !bool;
};
// 调用清空
let clearChildComponent = function (that, idName = '#hunt') {
  let child = that.selectComponent(idName);
  return function () {
    if (child) {
      child?.clearSear();
    } else {
      child = that.selectComponent(idName);
      child?.clearSear();
    }
  };
  // ps：使用方法 clearChildComponent(this)()
};
// 调用回填
let fillChildComponent = function (that, idName = '#hunt') {
  let child = that.selectComponent(idName);
  // console.log(idName, child)
  return function (tempObj) {
    if (child) {
      child.setBackfillData(tempObj);
    } else {
      child = that.selectComponent(idName);
      child.setBackfillData(tempObj);
    }
  };
  // ps：使用方法 fillChildComponent(this)(tempObj)
};
// 处理多选地区 有父级时，孩子不需要的情况
function handleMultiple(ary = []) {
  if (!ary.length) return [];
  let temParr = JSON.parse(JSON.stringify(ary));
  let checkedAry = temParr.filter(i => i.status == 'checked' && !i.ischildren);
  checkedAry = checkedAry.filter(
    item => !checkedAry.map(item => item.code).includes(item.parent)
  );
  if (!checkedAry.length) return ary;
  checkedAry.forEach(i => {
    temParr = temParr.filter(itm => i.code != itm.parent);
  });
  return temParr;
}
// 处理多选数据 --后面这里还需要写一个类似的方法 这种处理后的数据只能用于查询 不用作为回选
let handleData = function (params) {
  let obj = clone(params); //Object.assign
  // 处理对选相关数据
  obj['areas'] =
    params.area_code_list?.length > 0 ?
    handleMultiple(params.area_code_list)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  obj['trade_types'] =
    params.industry_code_list?.length > 0 ?
    handleMultiple(params.industry_code_list)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  obj['ent_type'] =
    params.ent_type?.length > 0 ?
    handleMultiple(params.ent_type)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  obj['enterprise_license_list'] =
    params.enterprise_license_list?.length > 0 ?
    handleMultiple(params.enterprise_license_list)
    .filter(i => i.status === 'checked')
    .map(i => i.code) : [];
  // 这个比较特殊
  // obj['industrial_list'] =
  //   params.industrial_list_data?.length > 0 ?
  //   params.industrial_list_data.filter(i => i.active === true).map(i => i.code) : [];

  return JSON.stringify(obj);
};
// 关闭企业名称弹窗
let closeEntNamePop = function (that, idName = '#hunt') {
  let child = that.selectComponent(idName);
  return function () {
    if (child) {
      child.closeInptPop();
    } else {
      child = that.selectComponent(idName);
      child?.closeInptPop();
    }
  };
};
// 处理高级搜索类似地区弹窗---名字哪里
let getNameFromPop = function (data) {
  // console.log(data)
  if (data?.length > 0 && data?.[0]?.MultipleCelectionSingleSelection) {
    //产业链单独处理
    return data[0].name;
  }
  let arr = [];
  data?.length > 0 &&
    data.forEach((i, idx, oldArr) => {
      if (i.status == 'checked') {
        if (i.parent && i.parent != '') {
          // 有父亲
          let parent = oldArr.filter(itm => i.parent == itm.code)[0];
          parent.status == 'checked' ? arr.push(parent) : arr.push(i);
        } else {
          // 没有父亲
          arr.push(i);
        }
      }
    });
  // 排重
  let newobj = {};
  arr = arr.reduce((preVal, curVal) => {
    newobj[curVal.code] ? '' : (newobj[curVal.code] = preVal.push(curVal));
    return preVal;
  }, []); //.sort((a, b) => (a.level - b.level))
  return arr.map(item => item.name).join('');
};
// 将'true','false'转换成==》true false 如果有前缀还要整合成对象形式
let handlestructure = function (data) {
  let params = Object.assign({}, data);
  boolConstant.forEach(item => {
    if (params[item]?.length) {
      params[item] = JSON.parse(params[item][0]); //将 "true"==>true
    } else {
      delete params[item];
    }
  });
  parentType.forEach(i => {
    Object.keys(params).forEach(itm => {
      if (itm.startsWith(i)) {
        let suffix = itm.slice(i.length),
          prefix = itm.slice(0, i.length - 1),
          obj = {};
        obj[suffix] = params[itm];
        params[prefix] = {
          ...params[prefix],
          ...obj
        };
        delete params[itm];
      }
    });
  });
  return params;
};
let getHeightStatus = function (paramsData) {
  //获取高亮状态
  let isHeight = false; //是否有选中的 外面设置高亮要用
  Object.keys(paramsData).some(keys => {
    //这里如果遇到以前的数据可能会报错，所以以前的数据后端要清除没法兼容
    // 这种有自定义需要单独处理
    if (
      searCheckPopConstant['list'].includes(keys) ||
      searMultIptConstant['list'].includes(keys)
    ) {
      if (
        paramsData[keys].length > 0 &&
        (paramsData[keys][0]?.start || paramsData[keys][0]?.end)
      ) {
        isHeight = true;
        return true;
      } else if (searInputConstant['list'].includes(keys)) {
        if (paramsData[keys].trim().length > 0) {
          isHeight = true;
          // console.log(1)
          return true;
        }
      } else {
        isHeight = false;
      }
    } else if (paramsData[keys].length > 0) {
      isHeight = true;
      return true;
    }
  });
  // console.log(isHeight)

  return isHeight;
};

module.exports = {
  // 企业搜索常量
  dateTagList,
  renderList,
  params,
  searRadioConstant,
  searMultiSelectConstant,
  searMultAllConstant,
  searMultPopConstant,
  searInputConstant,
  searCheckPopConstant,
  searMultIptConstant,
  searchTermList,
  // 方法
  checkoutSear,
  clearChildComponent,
  handleData,
  fillChildComponent,
  closeEntNamePop,
  getNameFromPop,
  handlestructure,
  getHeightStatus,
  handleMultiple
};