<!--components/BtmListMultGroupPop/index.wxml-->
<HalfScreenPop showFooter="{{true}}" changePadding disableAnimation="{{true}}" visible="{{true}}" position="{{position}}" bindclose="close" zIndex="{{100}}" bindsubmit="submit" 
title='{{title}}'>
  <view slot="customContent">
    <view class="btm-list-wrap">
      <!-- 列表内容 -->
      <scroll-view scroll-y class="list-container">
        <view wx:for="{{sourceData}}" wx:key="code" data-item="{{item}}" bindtap="handleItemClick" class="list-item {{item.active ? 'active' : ''}}">
          <view class="item-content">
            <view class="item-name">{{item.name}}</view>
            <view class="item-checkbox {{item.active ? 'checked' : ''}}">
              <view class="checkbox-icon" wx:if="{{item.active}}">✓</view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{sourceData.length === 0}}">
          <view class="empty-text">--暂无数据--</view>
        </view>
      </scroll-view>
    </view>
  </view>
</HalfScreenPop>