// 使用新的 BtmListMultGroupPop 渲染工具替代 hunt 组件
import {
  searRender
} from '../../../components/BtmListMultGroupPop/utils/searRender.js';

// 从 ConfigurableForm 引入相关方法
import {
  checkoutSear,
  clearChildComponent,
  handleData,
  fillChildComponent,
  closeEntNamePop
} from '../../../components/ConfigurableForm/utils/component-helpers';

import {
  hijack
} from '../../../utils/route';
import {
  handleShareUrl,
  getShareUrl
} from '../../../utils/mixin/pageShare';
var checkMixin = require('../../../utils/mixin/check');
import {
  home
} from '../../../service/api.js';
import {
  commonRequest
} from '../../../utils/mixin/loginbeforeReq';
import {
  hasPrivile
} from '../../../utils/route';
const app = getApp();

Page({
  behaviors: [checkMixin()],
  data: {
    isIphoneX: app.globalData.isIphoneX,
    // /------------------/
    saveSearVal: '', //保存选项值
    // 接收到的参数
    isHeight: false,
    paramsData: {},
    // 模版
    templateAry: [],
    updating: false, // 数据是否处于更新状态
    renderAry: [] //模版渲染数组
  },
  onLoad() {
    handleShareUrl();
  },
  onShow() {
    //公共弹窗的请求
    commonRequest();
  },
  handleLogin() {
    // 获取搜索模版
    this.getsearData();
  },
  getParams(e) {
    // 筛选后获取的数据
    let {
      isHeight,
      paramsData
    } = e.detail;
    this.setData({
      isHeight,
      paramsData
    });
  },
  /*--模版相关--*/
  onsearPop: hijack(
    async function () {
      // 搜索模版-弹窗
      let permission = await hasPrivile({
        packageType: true
      });
      if (permission == '普通VIP') {
        this.setData({
          vipVisible: true
        });
        return;
      }

      this.setData({
        searPop: true
      });
    }, {
      type: 'searterm',
      app: app
    }
  ),
  async getsearData() {
    let {
      items
    } = await home.searTemplate();
    let searRenderObj = {};
    if (!items) {
      // app.showToast('网络丢失,请稍后再试!')
      items = [];
      console.warn('获取数据失败!');
      return;
    }
    items.map((item, index) => {
      item.active = false;
      if (index === 0) {
        item.active = true;
        searRenderObj = JSON.parse(item['search_json'] || null);
      }
      return item;
    });
    let renderAry = searRender(searRenderObj);
    this.setData({
      templateAry: items,
      renderAry
    });
  },

  onsearItm(e) {
    //搜索模板高亮切換
    let {
      templateAry
    } = this.data;
    const {
      item: {
        search_json
      },
      index
    } = e.currentTarget.dataset;
    let renderObj = JSON.parse(search_json || null);
    let idx = templateAry.findIndex(i => i.active);
    if (index != idx) {
      templateAry[idx].active = false;
      templateAry[index].active = true;
    }
    let renderAry = searRender(renderObj);
    // 設置企業规模显示的数据
    this.setData({
      templateAry,
      renderAry
    });
  },
  async onsearDel(e) {
    //搜索模版-删除
    const {
      item,
      index
    } = e.currentTarget.dataset;
    const {
      id
    } = item;
    let {
      templateAry
    } = this.data;
    try {
      const res = await home.delTemplate(id);
      // 删除成功
      if (templateAry[index].active && templateAry.length >= 2) {
        //恰好删除的是高亮的这个

        let item, idx;
        if (index === 0) {
          item = templateAry[1];
          idx = 1;
        } else {
          item = templateAry[0];
          idx = 0;
        }
        this.onsearItm({
          currentTarget: {
            dataset: {
              item,
              index: idx
            }
          }
        });
      }
      templateAry.splice(index, 1);
      this.setData({
        templateAry
      });
    } catch (err) {
      console.log(err);
      app.showToast('删除失败,请稍后再试!');
    }
  },
  clearSear: hijack(
    function () {
      // 清空模版
      clearChildComponent(this, '#sear-hunt')();
    }, {
      type: 'searterm',
      app: app
    }
  ),
  saveSear: hijack(
    function () {
      const {
        isHeight,
        paramsData
      } = this.data;
      closeEntNamePop(this, '#sear-hunt')();
      console.log(111, isHeight, paramsData);
      // if (isHeight) {
      //   checkoutSear(paramsData) &&
      //     this.setData({
      //       saveSearPop: true,
      //       saveSearVal: '',
      //       updating: false
      //     });
      // } else {
      //   app.showToast('请选择筛选条件！', 'none', 1500);
      // }
    }, {
      type: 'searterm',
      app: app
    }
  ),
  saveSearCancel() {
    //取消
    this.setData({
      saveSearPop: false
    });
  },
  async saveSearSub() {
    //保存-确定
    let {
      saveSearVal,
      paramsData,
      updating
    } = this.data;
    if (updating) return;
    saveSearVal = saveSearVal.trim();
    if (!saveSearVal) {
      app.showToast('请输入模版名称!');
      return;
    }
    let copyObj = {
      title: saveSearVal,
      search_json: handleData(paramsData)
    };
    this.setData({
      updating: true
    });
    try {
      const res = await home.saveTemplate(copyObj);
      // 发请求 关闭弹窗
      this.saveSearCancel();
      app.showToast('模版新增完成!', 'none', 1500);
      this.getsearData(); //发送请求刷新数据
    } catch (err) {
      app.showToast('新增模版失败,请稍后再试!', 'none', 1500);
      console.log(err);
      this.saveSearCancel();
    }
  },
  submitSear: hijack(
    function () {
      //立即搜索--直接到搜索
      let obj;
      const {
        paramsData
      } = this.data;
      closeEntNamePop(this)();
      if (paramsData['est_date'].length) {
        paramsData['est_date'].map(i => {
          delete i['name'];
        });
      }
      obj = JSON.stringify(paramsData);
      if (!checkoutSear()) {
        return;
      }
      obj = encodeURIComponent(obj);
      app.route(this, `/childSubpackage/pages/huntList/searchs?str=${obj}`);
    }, {
      type: 'searterm',
      app: app
    }
  ),
  searsub() {
    //应用模版
    let {
      templateAry
    } = this.data,
      tempObj;
    if (templateAry.length <= 0) return;
    let {
      search_json
    } = templateAry.filter(item => item.active)[0];
    tempObj = JSON.parse(search_json);

    // 把时间换成针对当前时间戳 - 使用新的配置获取方式
    try {
      const {
        generateFieldConfigs
      } = require('../../../components/ConfigurableForm/config/fields');
      const fieldConfigs = generateFieldConfigs();
      const estDateField = fieldConfigs.find(
        field => field.type === 'est_date'
      );

      if (
        estDateField &&
        estDateField.list &&
        tempObj['est_date'] &&
        tempObj['est_date'].length
      ) {
        tempObj['est_date']
          .filter(i => !i.special)
          .map(item => {
            estDateField.list.forEach(itm => {
              if (item.name == itm.name) {
                item.start = itm.id.split('$')[0];
                item.end = itm.id.split('$')[1];
              }
            });
          });
      }
    } catch (error) {
      console.warn('无法获取时间配置，跳过时间处理:', error);
    }

    fillChildComponent(this)(tempObj);
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  },
  onShareAppMessage: function () {
    return {
      title: '邀请你使用企业猎搜，300+筛选维度供你选择', //自定义转发标题
      path: getShareUrl('/companyPackage/pages/searTerm/sear-term'), //分享页面路径
      imageUrl: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/aa2.png' //图片后面换
    };
  }
});