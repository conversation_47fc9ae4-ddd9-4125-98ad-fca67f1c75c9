import {
  params,
  searRadioConstant,
  searMultiSelectConstant,
  searMultAllConstant,
  searMultPopConstant,
  getHeightStatus,
  dateTagList,
  getNameFromPop,
  handleData
} from '../mixin.js';
import {
  clone
} from '../../../utils/util';
import {
  hijack
} from '../../../utils/route';
import {
  debounce,
  handleSearchHight
} from '../../../utils/formate';
import {
  common
} from '../../../service/api';
import {
  hasPrivile
} from '../../../utils/route';
const app = getApp();
module.exports = Behavior({
  properties: {
    wrapHeight: String, //容器高度
    isPage: Boolean, //用于设置所属行业样式
    filterAry: {
      //这个暂时没用到
      type: Array,
      value: []
    }
  },
  data: {
    isIphoneX: app.globalData.isIphoneX,
    params,
    login: app.globalData.login,
    minCapital: '', // 最低资本
    maxCapital: '', // 最高资本
    dateType: '', // 日期类型（开始/结束）
    dateActive: false, // 日期输入框活动状态
    date: '', // 日期组件回填数据
    // input类型
    capitalActive: false, // 注册资本输入框活动状态
    minDate: '', // 最低年限
    maxDate: '', // 最高年限
    socialActive: false,
    socialminPeson: '',
    socialmaxPeson: '',
    // /------------------/
    saveSearVal: '', //保存选项值
    // 弹窗
    searPop: false,
    saveSearPop: false,
    regionPop: false,
    datePop: false,
    eleseicPop: false,
    enttypePop: false,
    districtPop: false,
    chainCodePop: false,
    // 模版
    idName: '', //滚动到id名字的位置
    focus: false, //这里只处理单位名称
    searchList: [],
    templateAry: [],
    renderAry: [] //模版渲染数组
  },
  observers: {
    params: function (val) {
      console.log('params', val);
      this.result(val);
    }
  },
  methods: {
    // 组装回填数据 --外部直接调用子组件方法
    setBackfillData(tempObj = {}) {
      this.clearSear();
      let {
        itemList,
        params,
        minCapital,
        maxCapital,
        capitalActive,
        socialActive,
        socialminPeson,
        socialmaxPeson,
        minDate,
        maxDate,
        dateActive
      } = this.data;
      params = Object.assign(params, tempObj);
      // console.log(itemList)
      itemList = itemList.map(item => {
        for (let key in params) {
          if (item.type === key) {
            let tagId = '';
            // 根据数据类型设置对应的tagId
            switch (item.type) {
              case 'reg_capital': //  注册资本
                let strs = params[key].length ?
                  params[key][0].start + '$' + params[key][0].end :
                  '';
                let tagIdArr = [
                  '0$100',
                  '100$200',
                  '500$1000',
                  '1000$5000',
                  '5000$'
                ]; //这里后续要弄成三元 还有其它形式
                // 如果返回的注册资本的数据不是tag存在的数据则设置给input组件
                if (!tagIdArr.includes(strs)) {
                  minCapital = params[key].length ? params[key][0].start : '';
                  maxCapital = params[key].length ? params[key][0].end : '';

                  // 只要input存在数据，则设置为活动状态
                  if (minCapital || maxCapital) capitalActive = true;
                  tagId = strs;
                } else {
                  tagId = params[key];
                }
                //
                break;
              case 'insured_num': //  注册资本
                let strss = params[key].length ?
                  params[key][0].start + '$' + params[key][0].end :
                  '';
                let tagIdArrs = [
                  '0$49',
                  '50$99',
                  '100$499',
                  '500$999',
                  '1000$4999',
                  '5000$'
                ]; //这里后续要弄成三元 还有其它形式
                // 如果返回的注册资本的数据不是tag存在的数据则设置给input组件
                if (!tagIdArrs.includes(strss)) {
                  socialminPeson = params[key].length ?
                    params[key][0].start :
                    '';
                  socialmaxPeson = params[key].length ? params[key][0].end : '';

                  // 只要input存在数据，则设置为活动状态
                  if (socialminPeson || socialmaxPeson) socialActive = true;
                  tagId = strss;
                } else {
                  tagId = params[key];
                }
                //
                break;

              case 'est_date': //  注册时间
                let str = params[key].length ?
                  params[key][0].start + '$' + params[key][0].end :
                  '';
                // 如果返回的注册时间的数据不是tag存在的数据则设置给input组件
                if (!dateTagList.includes(str)) {
                  minDate = params[key].length ? params[key][0].start : '';
                  maxDate = params[key].length ? params[key][0].end : '';

                  // 只要input存在数据，则设置为活动状态
                  if (minDate || maxDate) dateActive = true;
                  tagId = str;
                } else {
                  tagId = params[key];
                }
                break;
              default:
                tagId = params[key];
                break;
            }
            // 设置地区-行业-企业类型，企业许可
            if (Object.keys(searMultPopConstant).includes(key)) {
              let obj = params[searMultPopConstant[key]['data']];
              if (obj?.length < 0) return;
              item.content = getNameFromPop(obj);
            } else if (
              searRadioConstant['list'].includes(key) ||
              searMultiSelectConstant['list'].includes(key)
            ) {
              // console.log(tagId)
              // 通过tagId比对判断是否选中
              item.list = item.list.map(tag => {
                const isArray = Array.isArray(tagId); // 判断是否是联系方式的回填数据
                if (isArray) {
                  for (let number of tagId) {
                    if (tag.id === number) {
                      tag.active = true;
                    }
                  }
                } else {
                  if (tag.id === tagId) {
                    tag.active = true;
                  }
                }
                return tag;
              });
            } else if (searMultAllConstant['list'].includes(key)) {
              item.list = item.list.map(tag => {
                Array.isArray(tagId) &&
                  tagId.length > 0 &&
                  tagId.forEach((itm, idx) => {
                    let ids =
                      params[key][idx].start + '$' + params[key][idx].end;
                    if (tag.id == ids) tag.active = true;
                  });
                return tag;
              });
            }
          }
        }
        return item;
      });
      // console.log(params)
      this.setData({
        params,
        itemList,
        minCapital,
        maxCapital,
        capitalActive,
        socialActive,
        socialminPeson,
        socialmaxPeson,
        minDate,
        maxDate,
        dateActive
      });
    },
    // 设置日期
    setDate({
      detail: {
        date
      }
    }) {
      let {
        minDate,
        maxDate,
        dateType
      } = this.data;
      //
      let name = '';
      if (date) {
        if (dateType === 'startDate') minDate = date;
        if (dateType === 'endDate') maxDate = date;
      }
      if (minDate) name = '最低年限' + minDate;
      if (maxDate) name += '最高年限' + maxDate;
      this.setTagStatus('est_date');
      // console.log(minDate, maxDate)
      this.setData({
          minDate,
          maxDate,
          dateActive: minDate || maxDate,
          'params.est_date': [{
            start: minDate,
            end: maxDate,
            special: true,
            name
          }]
        },
        () => this.result()
      );
    },

    // 将对应类型的tag的活动状态设为false-全部
    setTagStatus(type) {
      let {
        itemList,
        params
      } = this.data;
      for (let item of itemList) {
        // 清除对应类型标签的选中状态
        if (item.type === type) {
          item.list = item.list.map(tag => {
            tag.active = false;
            return tag;
          });
        }
      }
      params[type] = [];
      this.setData({
        itemList,
        params
      });
    },

    // 注册资本输入框获取焦点时清除选中的注册资本标签
    inputFocus(e) {
      this.setTagStatus(e.currentTarget.dataset.type);
    },
    inputChange(e) {
      const {
        minCapital,
        maxCapital,
        socialmaxPeson,
        socialminPeson
      } =
      this.data;
      // console.log(minCapital, maxCapital)
      let types = e.currentTarget.dataset.type;
      let type = 'params.' + types;
      // console.log(type)
      if (types == 'insured_num') {
        this.setData({
            socialActive: socialminPeson !== '' || socialmaxPeson !== '',
            [type]: [{
              start: socialminPeson,
              end: socialmaxPeson,
              special: true
            }]
          },
          () => this.result()
        );
      } else if (types == 'reg_capital') {
        this.setData({
            capitalActive: minCapital !== '' || maxCapital !== '',
            [type]: [{
              start: minCapital,
              end: maxCapital,
              special: true
            }]
          },
          () => this.result()
        );
      }
    },
    // 分割字符串
    divisionStr(str) {
      let sign = '$';
      let obj = {
        start: '',
        end: ''
      };
      if (str.indexOf(sign) > -1) {
        let arr = str.split(sign);
        obj.start = arr[0];
        obj.end = arr[1];
      }
      return obj;
    },

    // 选中标签
    async selectTag({
      currentTarget: {
        dataset: {
          id,
          type,
          name,
          item
        }
      }
    }) {
      let {
        itemList,
        params,
        minCapital,
        maxCapital,
        capitalActive,
        minDate,
        maxDate,
        socialminPeson,
        socialmaxPeson,
        socialActive,
        dateActive
      } = this.data;
      let searMultiSelect = [
        ...searRadioConstant['list'],
        ...searMultiSelectConstant['list']
      ];
      // 判断是不是VIP23.2.23
      // todo 25.6 后续放开
      // if (item?.vip) {
      //   let str = await hasPrivile({
      //     packageType: true
      //   })
      //   if (str == '游客') {
      //     app.route(this, '/pages/login/login')
      //     return
      //   } else if (str == '普通VIP') {
      //     this.setData({
      //       vipVisible: true
      //     })
      //     return
      //   }
      // }
      itemList = itemList.map(item => {
        if (item.type === type) {
          item.list = item.list.map(tag => {
            if (searRadioConstant['list'].includes(type)) tag.active = false; //单选
            if (tag.id === id) {
              //  if (type === 'fixed_phone_flag' || type === 'contacts_style')
              if (searRadioConstant['list'].includes(type)) {
                //单选
                if (params[type] == id) {
                  tag.active = false;
                  params[type] = [];
                } else {
                  tag.active = true;
                  params[type] = [id]; //单选-并且上传数组形式
                }
              }
              if (searMultiSelectConstant['list'].includes(type)) {
                //多选
                let arr = params[type];
                if (arr.includes(id)) {
                  arr.splice(
                    arr.findIndex(i => i === id),
                    1
                  );
                  tag.active = false;
                } else {
                  //
                  arr.push(id); //这里有问题 不应该pushid
                  tag.active = true;
                }
              }
              if (!searMultiSelect.includes(type)) {
                //有start和end这种
                let obj = this.divisionStr(id);
                //
                params[type] = params[type].filter(i => !i.special);
                let arr = params[type];
                let idx = -1;
                if (type == 'est_date') {
                  idx = arr.findIndex(
                    i => i.start === obj.start && i.end === obj.end
                  );
                } else {
                  idx = arr.findIndex(
                    i => JSON.stringify(i) == JSON.stringify(obj)
                  );
                }
                if (idx >= 0) {
                  arr.splice(idx, 1);
                  tag.active = false;
                } else {
                  //
                  if (type === 'est_date') {
                    obj['name'] = name;
                  }
                  arr.push(obj); //这里有问题 不应该pushid
                  tag.active = true;
                }
                if (type === 'reg_capital') {
                  minCapital = maxCapital = '';
                  capitalActive = false;
                }
                if (type === 'insured_num') {
                  socialminPeson = socialmaxPeson = '';
                  socialActive = false;
                }
                if (type === 'est_date') {
                  minDate = maxDate = '';
                  dateActive = false;
                }
              }
            }
            return tag;
          });
        }
        return item;
      });
      //
      this.setData({
        itemList,
        minCapital,
        maxCapital,
        capitalActive,
        minDate,
        maxDate,
        socialminPeson,
        socialmaxPeson,
        socialActive,
        dateActive,
        dateActive,
        params
      });
    },
    // 设置params中注册资本/注册日期的数据
    setParmas(item, params, start, end) {
      let bl = item.list.every(tag => {
        return tag.active === false;
      });

      // 如果注册资本/注册日期没有标签选中,则使用input的值
      if (bl)
        params[item.type][0] = {
          start,
          end,
          special: true
        };
    },
    // 更多筛选---相关
    // 显示日期选择器
    showtDatePicker({
      currentTarget: {
        dataset: {
          type
        }
      }
    }) {
      let {
        date,
        minDate,
        maxDate
      } = this.data;
      date = type === 'startDate' ? minDate : maxDate;
      this.setData({
        datePop: true,
        dateType: type,
        date
      });
    },
    submitSub(e) {
      //多选弹窗回调
      const {
        itemList
      } = this.data;
      let {
        checkedList: obj,
        mark
      } = e.detail;
      let str = 'params.' + searMultPopConstant[mark]['data'];
      if (obj.length < 0) return;
      let name = getNameFromPop(obj);
      itemList.forEach(item => item.type == mark && (item.content = name));
      this.setData({
          [str]: obj,
          itemList,
          chainCodePop: false, //写死特殊情况
          enttypePop: false,
          regionPop: false,
          eleseicPop: false
        },
        () => this.result()
      );
    },
    onexpPop() {
      //企业说明弹窗
      this.setData({
        isexpPop: true
      });
    },
    // 弹窗
    handlePop(e) {
      const {
        item: {
          type,
          title
        }
      } = e.currentTarget.dataset;
      // console.log(type,title)
      switch (
        title //这里目前写死
      ) {
        case '所在地区':
          this.setData({
            regionPop: true
          });
          break;
        case '所属行业':
          this.setData({
            eleseicPop: true
          });
          break;
        case '企业类型':
          this.setData({
            enttypePop: true
          });
          break;
        case '企业许可':
          this.setData({
            districtPop: true
          });
          break;
        case '新兴产业':
          this.setData({
            chainCodePop: true
          });
          break;

        default:
          break;
      }
    },
    callPhone() {
      wx.makePhoneCall({
        phoneNumber: '19923989873'
      });
    },

    result(val) {
      //自定义需要单独调用，弹窗那种也需要调用
      let isHeight = false; //是否有选中的 外面设置高亮要用
      let paramsData = val ? val : this.data.params;
      isHeight = getHeightStatus(paramsData);
      paramsData = handleData(paramsData);
      paramsData = JSON.parse(paramsData);
      //把结果抛出去
      // console.log('传出去的结果:', paramsData, isHeight)
      this.setData({
        paramsData,
        isHeight
      });
      this.triggerEvent('submit', {
        isHeight,
        paramsData: clone(paramsData)
      });
    },
    // input框相关
    onFocus() {
      this.setData({
        focus: true
      });
    },
    onBlur() {
      // this.setData({ focus: false })
    },
    checkLogin: hijack(function () {}, {
      app,
      type: 'huntSear'
    }),
    onInput(e) {
      const that = this;
      const value = e.detail.value;
      this.setData({
          'params.ent_name': value
        },
        () => {
          that.getSearchList(value);
          this.result();
        }
      );
      //节流-- 发请求--获取搜索列表数据
    },
    getSearchList: debounce(function ([...val]) {
      const that = this;
      let value = val[0].toString();
      this.setData({
        searchList: []
      });
      common.getNameList(value).then(res => {
        if (res.length <= 0) {
          that.setData({
            text: '请输入更精确的内容!'
          });
          return;
        }
        const data = handleSearchHight(
          res,
          'ent_name',
          this.data.params.ent_name
        );
        this.setData({
          searchList: data
        });
      });
    }, 1000),

    clickItem(e) {
      const that = this;
      // 点击单个
      let {
        ent_name
      } = e.currentTarget.dataset.item; //这里的item是数组，把它转成字符串 值.
      let str = ent_name.join('');
      //id用来判断是不是数据库里面的
      this.setData({
          'params.ent_name': str
        },
        () => {
          that.setData({
            focus: false
          });
          that.result();
        }
      );
    },
    closeInptPop(e) {
      this.setData({
        focus: false
      });
    },
    // 样式
    closeleft(e) {
      //展开关闭右边
      let {
        leftList
      } = this.data;
      let {
        item: {
          title,
          isOpen
        }
      } = e.currentTarget.dataset;
      leftList.some(item => {
        if (item.title == title) {
          item.isOpen = !isOpen;
          return true;
        }
      });
      this.setData({
        leftList
      });
    },
    leftactvie(e) {
      let {
        leftList,
        idName
      } = this.data;
      let {
        item: {
          title,
          isActive
        },
        itm
      } = e.currentTarget.dataset;
      leftList.some(item => {
        if (item.onlyText) {
          item.isActive = false;
          item.map.forEach(i => (i.active = false));
        }
        if (item.title == title) {
          item.map.forEach(i => {
            // i.active = false
            if (i.key == itm) {
              i.active = true;
              item.isActive = true;
              idName = i.key;
            }
          });
        }
      });
      this.setData({
        leftList,
        idName
      });
    },
    openright(e) {
      let {
        item: {
          type,
          isOpen
        }
      } = e.currentTarget.dataset;
      let {
        itemList
      } = this.data;
      // console.log(item)
      itemList.some(i => {
        if (i.type == type) {
          i.isOpen = !isOpen;
          return true;
        }
      });
      this.setData({
        itemList
      });
    }
  }
});