<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<view class="order">
    <view class="select-box">
        <Select options="{{selectData}}" bindselect="onSelect"></Select>
    </view>
    <!-- 卡片 -->
    <view>
        <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: calc(100vh - 88rpx); background-color: #F7F7F7;">
            <view wx:if="{{!bazaarIsNull}}" class="bus-card">
                <block wx:for="{{bazaarlist}}" wx:key='index'>
                    <!-- 单个卡片 -->
                    <view class="or-card">
                        <!-- 标题 -->
                        <view class="or-title">
                            <view class="or-title-l">
                              {{item.subject}}
                            </view>
                            <!-- 待支付->false -->
                            <view class="or-title-r {{item.payment_status_list}}">{{item.paymentStatus}}</view>
                        </view>
                        <!-- 内容 -->
                        <view class="or-cont">
                            <view>订单号码：{{item.pay_code}} </view>
                            <view>下单时间：{{item.create_time}} </view>
                            <view>付款方式：{{item.payment_method}}</view>
                        </view>
                        <!-- 底部 -->
                        <view class="or-foot">
                            <view class="or-foot-l">
                                <text>付款金额：</text>￥{{item.money}}
                            </view>
                            <!-- <view class="or-foot-r" wx:if="{{item['payment_status_list']=='UNPAID'}}" bindtap="gopay" data-item="{{item}}">
                                去付款
                            </view> -->
                        </view>
                    </view>
                </block>
                <view wx:if="{{bazaarlist.length>=bazaarParms.$limit}}" style="width: 100%;">
                    <template is='more' data="{{hasData:bazaarHasData}}"></template>
                </view>
            </view>
            <!--暂无数据 -->
            <view wx:else style="width: 100%;height: calc(100vh - 88rpx)">
                <template is='null' data="{{errTips:'暂无订单'}}"></template>
            </view>
        </scroll-view>
    </view>
    


</view>