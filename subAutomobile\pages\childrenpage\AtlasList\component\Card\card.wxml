<scroll-view refresher-enabled bindrefresherrefresh="handleRefresher" refresher-triggered="{{isTriggered}}" class="list" bindscrolltolower="loadMore" scroll-y="{{true}}">
    <view class="search-card" wx:for="{{souceData}}" wx:key="index">
        <view class="card-boxs">
            <view class="card-logo" wx:if="{{false}}">
                <image src="{{item.logo || '/image/chain_logo.png'}}" mode="aspectFit"></image>
            </view>
            <view class="card-head">
                <view class="card_h">
                    <view class="card_h_l text-ellipsis " bindtap="goDetail" data-item="{{item}}">{{item.ent_name}}</view>
                    <view class="card_h_r" bindtap="collect" data-item="{{item}}" data-index="{{index}}">
                        <!-- 后面根据具体传进来的字段判断 -->
                        <view class="card_h_r_img">
                            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" wx:if="{{!item.collect}}"></image>
                            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" wx:else></image>
                        </view>
                        <text>收藏</text>
                    </view>
                </view>
                <view class="card_tag">
                    <view class="card_tag_box">
                        <text class="{{tagItem.tagColor}}" wx:for="{{item.tags}}" wx:for-item="tagItem" wx:key="tagName">{{tagItem.tagName}}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="card_c">
            <view class="card_c_i card_c_is">
                <text class="name">法人代表人</text>
                <text class="cont">{{item.legal_person}}</text>
            </view>
            <view class="card_c_i card_c_is">
                <text class="name">注册资本</text>
                <text class="cont">{{item.reg_capital}}万</text>
            </view>
            <view class="card_c_i">
                <text class="name">成立日期</text>
                <text class="cont">{{item.register_date}}</text>
            </view>
        </view>
        <view class="card_ico">
            <view class="card_ico_i" bindtap="relation" data-item="{{item}}">
                <view class="card_ico_i_img">
                    <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
                </view>
                <view>
                    联系方式
                </view>
            </view>
            <view wx:if="{{item.official_website}}" class="card_ico_i" bindtap="official" data-item="{{item}}">
                <view class="card_ico_i_img">
                    <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
                </view>
                <view>
                    官网
                </view>
            </view>
            <view class="card_ico_i" bindtap="site" data-item="{{item}}">
                <view class="card_ico_i_img">
                    <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
                </view>
                <view>
                    地址
                </view>
            </view>
        </view>
    </view>
</scroll-view>