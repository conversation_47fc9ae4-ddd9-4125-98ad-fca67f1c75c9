/**
 * ConfigurableForm 主组件
 * 统一的可配置表单组件，支持不同变体
 */
const createConfigurableBehavior = require('./core/behavior-simple');
const {
  generateRenderList
} = require('./core/renderer');
const {
  CONFIG_TYPES,
  CUSTOMFIELD
} = require('./config/constants');

Component({
  behaviors: [createConfigurableBehavior()],

  properties: {
    // 组件变体：'full' | 'simplified'
    variant: {
      type: String,
      value: CONFIG_TYPES.FULL
    },

    // 容器高度
    wrapHeight: {
      type: String,
      value: ''
    },

    // 是否为页面模式（影响某些样式）
    isPage: {
      type: Boolean,
      value: false
    },

    // 排除的字段列表
    excludeFields: {
      type: Array,
      value: []
    },

    // 自定义字段配置 - 默认字段配置 
    customFields: {
      type: Object,
      value: {
        ...CUSTOMFIELD
      }
    },

    // 是否启用VIP检查
    enableVipCheck: {
      type: <PERSON><PERSON><PERSON>,
      value: true
    },

    // 是否启用搜索功能
    enableSearch: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 组件内部状态
    initialized: false
  },

  observers: {
    'variant, excludeFields, customFields': function (
      variant,
      excludeFields,
      customFields
    ) {
      if (this.data.initialized) {
        this._updateRenderList();
      }
    }
  },

  lifetimes: {
    attached: function () {
      this._initializeComponent();
    },

    detached: function () {
      this._cleanup();
    }
  },

  methods: {
    /**
     * 初始化组件
     */
    _initializeComponent: function () {
      console.log(
        'ConfigurableForm initializing with variant:',
        this.data.variant
      );
      // 设置高度 
      let {
        wrapHeight,
        isIphoneX
      } = this.data
      if (!wrapHeight) {
        this.setData({
          wrapHeight: `calc(100vh - ${isIphoneX ? '168rpx' : '110rpx'})`
        })
      }
      // 渲染
      this._updateRenderList();
      this.setData({
        initialized: true
      });

      // 触发初始化完成事件
      this.triggerEvent('ready', {
        variant: this.data.variant
      });
    },

    /**
     * 更新渲染列表
     */
    _updateRenderList: function () {
      const data = this.data;
      const variant = data.variant;
      const excludeFields = data.excludeFields;
      const customFields = data.customFields;

      const renderConfig = {
        variant: variant,
        excludeFields: excludeFields,
        customFields: customFields,
        context: {
          vipStatus: this._getVipStatus()
        }
      };
      // console.log('135renderConfig', renderConfig);
      const renderResult = generateRenderList(renderConfig);
      const leftList = renderResult.leftList;
      const itemList = renderResult.itemList;
      console.log('右侧数据结构:', itemList);
      const config = renderResult.config;

      this.setData({
        leftList: JSON.parse(JSON.stringify(leftList)),
        itemList: JSON.parse(JSON.stringify(itemList)),
        params: JSON.parse(JSON.stringify(config.defaultParams))
      });

      console.log('Render list updated:', {
        leftList: leftList,
        itemList: itemList
      });
    },

    /**
     * 获取VIP状态
     */
    _getVipStatus: function () {
      // 这里可以根据实际需求获取VIP状态
      return this.data.login || false;
    },

    /**
     * 清空搜索表单（重写父类方法以支持动态配置）
     */
    clearSear: function () {
      this._updateRenderList();

      // 重置其他状态
      this.setData({
        minCapital: '',
        maxCapital: '',
        minDate: '',
        maxDate: '',
        date: '',
        capitalActive: false,
        socialActive: false,
        socialminPeson: '',
        socialmaxPeson: '',
        dateActive: false,
        dateType: '',
        focus: false,
        searchList: []
      });

      console.log('Form cleared for variant:', this.data.variant);
    },

    //  左侧菜单点击事件
    closeleft(e) {
      const item = e.currentTarget.dataset.item;
      const {
        leftList
      } = this.data;

      const updatedLeftList = leftList.map(leftItem => {
        if (leftItem.title === item.title) {
          return {
            ...leftItem,
            isOpen: !leftItem.isOpen,
          };
        }
        return {
          ...leftItem,
        };
      });

      this.setData({
        leftList: updatedLeftList
      });
    },
    //   左侧子菜单点击事件
    leftactvie(e) {
      const {
        item,
        itm
      } = e.currentTarget.dataset;
      const {
        leftList
      } = this.data;
      // 更新左侧菜单状态
      const updatedLeftList = leftList.map(leftItem => {
        leftItem.isActive = false
        leftItem.map.forEach(i => i.active = false)
        if (leftItem.title === item.title) {
          leftItem.isActive = true
          return {
            ...leftItem,
            map: leftItem.map.map(mapItem => ({
              ...mapItem,
              active: mapItem.key === itm
            }))
          };
        }
        return leftItem;
      });
      // console.log(22, updatedLeftList);
      this.setData({
        leftList: updatedLeftList,
        idName: itm // 滚动到对应位置
      });
    },

    // 右侧展开/收起
    openright(e) {
      const item = e.currentTarget.dataset.item;
      const {
        itemList
      } = this.data;

      const updatedItemList = itemList.map(listItem => {
        if (listItem.type === item.type) {
          return {
            ...listItem,
            isOpen: !listItem.isOpen
          };
        }
        return listItem;
      });

      this.setData({
        itemList: updatedItemList
      });
    },

    /**
     * 处理弹窗显示
     */
    handlePop(e) {
      const item = e.currentTarget.dataset.item;
      const popupMap = {
        areas: 'regionPop',
        trade_types: 'eleseicPop',
        ent_type: 'enttypePop',
        enterprise_license_list: 'districtPop'
      };

      const popupKey = popupMap[item.type];
      if (popupKey) {
        this.setData({
          [popupKey]: true
        });
      }
    },

    /**
     * 关闭输入弹窗
     */
    closeInptPop() {
      this.setData({
        focus: false
      });
    },

    /**
     * 输入框聚焦
     */
    onFocus() {
      this.setData({
        focus: true
      });
    },

    /**
     * 输入框失焦
     */
    onBlur() {
      // 延迟关闭，避免点击搜索项时立即关闭
      setTimeout(() => {
        this.setData({
          focus: false
        });
      }, 200);
    },
    // 输入框输入
    onInput(e) {
      const value = e.detail.value;
      this.setData({
        'params.ent_name': value
      });

      // 这里可以添加搜索建议逻辑
      if (value.length > 0) {
        this._searchSuggestions(value);
      } else {
        this.setData({
          searchList: []
        });
      }
    },
    // 搜索--请求服务器
    _searchSuggestions(keyword) {
      // 模拟搜索建议，实际项目中应该调用API
      const mockSuggestions = [{
          ent_name: [keyword, '科技有限公司']
        },
        {
          ent_name: [keyword, '贸易有限公司']
        },
        {
          ent_name: [keyword, '实业有限公司']
        }
      ];

      this.setData({
        searchList: mockSuggestions
      });
    },

    /**
     * 点击搜索建议项
     */
    clickItem(e) {
      const item = e.currentTarget.dataset.item;
      const entName = item.ent_name.join('');

      this.setData({
        'params.ent_name': entName,
        focus: false,
        searchList: []
      });
    },

    /**
     * 阻止滚动穿透
     */
    return () {
      return false;
    },

    /**
     * 清理资源
     */
    _cleanup() {
      // 清理定时器、事件监听器等
      console.log('ConfigurableForm cleanup');
    },

    /**
     * 获取当前表单数据
     */
    getFormData() {
      return {
        params: this.data.params,
        variant: this.data.variant
      };
    },

    /**
     * 设置表单数据
     */
    setFormData(data) {
      if (data.params) {
        this.setBackfillData(data.params);
      }
    },

    /**
     * 验证表单
     */
    validateForm() {
      return this.validateSearchParams();
    }
  }
});