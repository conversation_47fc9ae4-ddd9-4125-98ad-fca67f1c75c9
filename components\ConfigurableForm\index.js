/**
 * ConfigurableForm 主组件
 * 统一的可配置表单组件，支持不同变体
 */
import {
  hasPrivile
} from '../../utils/route';
const createConfigurableBehavior = require('./core/behavior-simple');
const {
  generateRenderList
} = require('./core/renderer');
const {
  CONFIG_TYPES,
  CUSTOMFIELD
} = require('./config/constants');
import {
  common
} from '../../service/api';

// 引入工具模块
const {
  dataHelpers,
  validationHelpers,
  formatHelpers,
} = require('./utils/helpers');
const {
  handleMultiple
} = require('./utils/data-processor');
const {
  componentHelpers,
  eventHelpers
} = require('./utils/component-helpers');
const PopupManager = require('./utils/popup-manager');
const {
  SEARCH_CONSTANTS,
  POPUP_FIELD_MAPPING,
} = require('./config/fields');
const app = getApp()
Component({
  behaviors: [createConfigurableBehavior()],

  properties: {
    // 组件变体：'full' | 'simplified'
    variant: {
      type: String,
      value: CONFIG_TYPES.FULL
    },

    // 容器高度
    wrapHeight: {
      type: String,
      value: ''
    },

    // 是否为页面模式（影响某些样式）
    isPage: {
      type: Boolean,
      value: false
    },

    // 排除的字段列表
    excludeFields: {
      type: Array,
      value: []
    },

    // 自定义字段配置 - 默认字段配置
    customFields: {
      type: Object,
      value: {
        ...CUSTOMFIELD
      }
    },

    //是否隐藏左侧
    isHideLeft: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 组件内部状态
    initialized: false,

    // 弹窗配置列表 - 用于模板循环渲染
    popupConfigs: POPUP_FIELD_MAPPING
  },

  observers: {
    'variant, excludeFields, customFields': function (
      variant,
      excludeFields,
      customFields
    ) {
      if (this.data.initialized) {
        this._updateRenderList();
      }
    },
    vipVisible(val) {
      // 通过事件传递的方式告诉外面，需要vip弹窗
      this.triggerEvent('vip', true)
    }
  },

  lifetimes: {
    attached: function () {
      this._initializeComponent();
    },

    detached: function () {
      this._cleanup();
    }
  },

  methods: {
    /**
     * 初始化组件
     */
    _initializeComponent: function () {
      console.log(
        'ConfigurableForm initializing with variant:',
        this.data.variant
      );
      // 设置高度
      let {
        wrapHeight,
        isIphoneX
      } = this.data;
      if (!wrapHeight) {
        this.setData({
          wrapHeight: `calc(100vh - ${isIphoneX ? '168rpx' : '110rpx'})`
        });
      }
      // 渲染
      this._updateRenderList();
      this.setData({
        initialized: true
      });

      // 触发初始化完成事件
      this.triggerEvent('ready', {
        variant: this.data.variant
      });
    },

    /**
     * 更新渲染列表
     */
    _updateRenderList: function () {
      const data = this.data;
      const variant = data.variant;
      const excludeFields = data.excludeFields;
      const customFields = data.customFields;

      const renderConfig = {
        variant: variant,
        excludeFields: excludeFields,
        customFields: customFields,
        context: {
          vipStatus: this._getVipStatus()
        }
      };
      // console.log('135renderConfig', renderConfig);
      const renderResult = generateRenderList(renderConfig);
      const leftList = renderResult.leftList;
      const itemList = renderResult.itemList;
      // console.log('右侧数据结构:', itemList);
      const config = renderResult.config;

      this.setData({
        leftList: JSON.parse(JSON.stringify(leftList)),
        itemList: JSON.parse(JSON.stringify(itemList)),
        params: JSON.parse(JSON.stringify(config.defaultParams))
      });

      console.log('Render list updated:', {
        leftList: leftList,
        itemList: itemList
      });
    },

    /**
     * 获取VIP状态
     */
    _getVipStatus: function () {
      // 这里可以根据实际需求获取VIP状态
      return this.data.login || false;
    },

    /**
     * 清空搜索表单（重写父类方法以支持动态配置）
     */
    clearSear: function () {
      this._updateRenderList();

      // 重置其他状态
      this.setData({
        minCapital: '',
        maxCapital: '',
        minDate: '',
        maxDate: '',
        date: '',
        capitalActive: false,
        socialActive: false,
        socialminPeson: '',
        socialmaxPeson: '',
        dateActive: false,
        dateType: '',
        focus: false,
        searchList: []
      });

      console.log('Form cleared for variant:', this.data.variant);
    },

    //  左侧菜单点击事件
    closeleft(e) {
      const item = e.currentTarget.dataset.item;
      const {
        leftList
      } = this.data;

      const updatedLeftList = leftList.map(leftItem => {
        if (leftItem.title === item.title) {
          return {
            ...leftItem,
            isOpen: !leftItem.isOpen
          };
        }
        return {
          ...leftItem
        };
      });

      this.setData({
        leftList: updatedLeftList
      });
    },
    //   左侧子菜单点击事件
    leftactvie(e) {
      const {
        item,
        itm
      } = e.currentTarget.dataset;
      const {
        leftList
      } = this.data;
      // 更新左侧菜单状态
      const updatedLeftList = leftList.map(leftItem => {
        leftItem.isActive = false;
        leftItem.map.forEach(i => (i.active = false));
        if (leftItem.title === item.title) {
          leftItem.isActive = true;
          return {
            ...leftItem,
            map: leftItem.map.map(mapItem => ({
              ...mapItem,
              active: mapItem.key === itm
            }))
          };
        }
        return leftItem;
      });
      // console.log(22, updatedLeftList);
      this.setData({
        leftList: updatedLeftList,
        idName: itm // 滚动到对应位置
      });
    },

    // 右侧展开/收起
    openright(e) {
      const item = e.currentTarget.dataset.item;
      const {
        itemList
      } = this.data;

      const updatedItemList = itemList.map(listItem => {
        if (listItem.type === item.type) {
          return {
            ...listItem,
            isOpen: !listItem.isOpen
          };
        }
        return listItem;
      });

      this.setData({
        itemList: updatedItemList
      });
    },

    /**
     * 处理弹窗显示 - 使用 PopupManager
     */
    handlePop(e) {
      const item = e.currentTarget.dataset.item;
      console.log('打开弹窗:', item);

      const updateData = PopupManager.openPopup(item.type);
      if (Object.keys(updateData).length > 0) {
        this.setData(updateData);
      }
    },

    /**
     * 统一的弹窗关闭方法 - 使用 PopupManager
     */
    closePopup(popupType) {
      const updateData = PopupManager.closePopup(popupType);
      if (Object.keys(updateData).length > 0) {
        this.setData(updateData);
      }
    },

    /**
     * 关闭所有弹窗 - 使用 PopupManager
     */
    closeAllPopups() {
      const updateData = PopupManager.closeAllPopups();
      this.setData(updateData);
    },

    /**
     * 关闭输入弹窗
     */
    closeInptPop() {
      this.setData({
        focus: false
      });
    },

    /**
     * 输入框聚焦
     */
    onFocus() {
      this.setData({
        focus: true
      });
      const {
        params
      } = this.data;
      if (!params.ent_name) return;
      this.onInput({
        detail: {
          value: params.ent_name
        }
      });
    },

    /**
     * 输入框失焦
     */
    onBlur() {
      // 延迟关闭，避免点击搜索项时立即关闭
      setTimeout(() => {
        this.setData({
          focus: false
        });
      }, 200);
    },
    // 输入框输入 - 使用防抖处理
    onInput: eventHelpers.createEventHandler(
      function (e) {
        const that = this;
        const value = e.detail.value;
        componentHelpers.updateComponentState(
          this, {
            'params.ent_name': value
          },
          () => {
            that._handleSearchSuggestions(value);
            that.result();
          }
        );
      }, {
        debounce: true,
        delay: 1200
      }
    ),
    // 搜索建议处理 - 使用工具模块
    async _handleSearchSuggestions(value) {
      if (!value || value.length === 0) {
        this.setData({
          searchList: []
        });
        return;
      }
      try {
        const res = await common.getNameList(value);
        // console.log(00, res);
        if (res && res.length > 0) {
          const data = this._processSearchResults(res, value);
          this.setData({
            searchList: data
          });
        } else {
          this.setData({
            searchList: [],
            text: '请输入更精确的内容!'
          });
        }
      } catch (err) {
        this.setData({
          searchList: [],
          text: '搜索失败，请重试'
        });
      }
    },

    // 处理搜索结果
    _processSearchResults(data, keyword) {
      return data.map(item => {
        if (item.ent_name && Array.isArray(item.ent_name)) {
          return item;
        }
        // 转换为高亮格式
        const text = item.ent_name || '';
        const parts = text.split(keyword);
        const highlighted = [];
        parts.forEach((part, index) => {
          if (index > 0) highlighted.push(keyword);
          if (part) highlighted.push(part);
        });
        return {
          ...item,
          ent_name: highlighted
        };
      });
    },

    /**
     * 点击搜索建议项
     */
    clickItem(e) {
      const that = this;
      const item = e.currentTarget.dataset.item;
      const entName = item.ent_name.join('');

      componentHelpers.updateComponentState(
        this, {
          'params.ent_name': entName,
          focus: false,
          searchList: []
        },
        () => {
          that.result();
        }
      );
    },

    // 标签选择逻辑 - 使用工具模块
    async selectTag(e) {
      const {
        id,
        type,
        item
      } = e.currentTarget.dataset;
      let {
        itemList,
        params
      } = this.data;
      // VIP检查
      if (item?.vip) {
        const vipStatus = await this._checkVipStatus();
        if (!vipStatus) return;
      }
      // console.log(id, type, item);
      // 获取字段配置
      const fieldConfig = this._getFieldConfig(type);
      // true head_ent_flag {title: "龙头企业", type: "head_ent_flag", list: Array(2), vip: true, betweenTopBoder: true}
      // console.log(22, fieldConfig);
      // 更新itemList和params
      itemList = itemList.map(listItem => {
        if (listItem.type === type) {
          return this._updateFieldSelection(listItem, id, fieldConfig);
        }
        return listItem;
      });

      // 清除相关输入框状态
      this._clearRelatedInputs(type);

      componentHelpers.updateComponentState(
        this, {
          itemList,
          params
        },
        () => {
          this.result();
        }
      );
    },

    /**
     * 获取字段配置
     */
    _getFieldConfig(type) {
      return {
        isRadio: SEARCH_CONSTANTS.RADIO_FIELDS.includes(type),
        isMultiSelect: SEARCH_CONSTANTS.MULTI_SELECT_FIELDS.includes(type),
        isRangeInput: SEARCH_CONSTANTS.RANGE_INPUT_FIELDS.includes(type)
      };
    },

    /**
     * 更新字段选择状态
     */
    _updateFieldSelection(listItem, id, fieldConfig) {
      const {
        params
      } = this.data;
      const type = listItem.type;

      if (!listItem.list) return listItem;

      const updatedList = listItem.list.map(tag => {
        if (fieldConfig.isRadio) {
          // 单选逻辑
          tag.active = false;
          if (tag.id === id) {
            if (params[type] && params[type][0] === id) {
              tag.active = false;
              params[type] = [];
            } else {
              tag.active = true;
              params[type] = [id];
            }
          }
        } else if (fieldConfig.isMultiSelect) {
          // 多选逻辑
          if (tag.id === id) {
            tag.active = !tag.active;
            if (!params[type]) params[type] = [];

            if (tag.active) {
              if (!params[type].includes(id)) {
                params[type].push(id);
              }
            } else {
              const index = params[type].indexOf(id);
              if (index > -1) {
                params[type].splice(index, 1);
              }
            }
          }
        } else if (fieldConfig.isRangeInput) {
          // 范围输入逻辑
          // debugger
          if (tag.id === id) {
            const obj = formatHelpers.splitStringToObject(id);
            if (!params[type]) params[type] = [];

            // 移除自定义输入的项
            params[type] = params[type].filter(i => !i.special);

            const existingIndex = params[type].findIndex(
              i => `${i.start}&${i.end}` === `${obj.start}&${obj.end}`
            );

            if (existingIndex >= 0) {
              params[type].splice(existingIndex, 1);
              tag.active = false;
            } else {
              if (type === 'est_date') {
                obj.name = tag.name;
              }
              params[type].push(obj);
              tag.active = true;
            }
          }
        }
        return tag;
      });

      return {
        ...listItem,
        list: updatedList
      };
    },

    /**
     * 清除相关输入框状态
     */
    _clearRelatedInputs(type) {
      const clearData = {};

      if (type === 'reg_capital') {
        clearData.minCapital = '';
        clearData.maxCapital = '';
        clearData.capitalActive = false;
      } else if (type === 'insured_num') {
        clearData.socialminPeson = '';
        clearData.socialmaxPeson = '';
        clearData.socialActive = false;
      } else if (type === 'est_date') {
        clearData.minDate = '';
        clearData.maxDate = '';
        clearData.dateActive = false;
      }

      if (Object.keys(clearData).length > 0) {
        componentHelpers.updateComponentState(this, clearData);
      }
    },

    // VIP状态检查
    async _checkVipStatus() {
      // todo 后续做完了再放开
      // let str = await hasPrivile({
      //   packageType: true
      // })
      // if (str == '游客') {
      //   app.route(this, '/pages/login/login')
      //   return false
      // } else if (str == '普通VIP') {
      //   this.setData({
      //     vipVisible: true
      //   })
      //   return false
      // }
      return true;
    },

    /**
     * 阻止滚动穿透
     */
    return () {
      return false;
    },

    /**
     * 清理资源
     */
    _cleanup() {
      // 清理定时器、事件监听器等
      console.log('ConfigurableForm cleanup');
    },

    /**
     * 获取当前表单数据
     */
    getFormData() {
      return {
        params: this.data.params,
        variant: this.data.variant
      };
    },

    /**
     * 设置表单数据
     */
    setFormData(data) {
      if (data.params) {
        this.setBackfillData(data.params);
      }
    },

    /**
     * 验证表单 - 使用工具模块
     */
    validateForm() {
      return validationHelpers.checkoutSear(this.data.params);
    },

    /**
     * 验证搜索参数 - 使用工具模块
     */
    validateSearchParams(paramsData) {
      const params = paramsData || this.data.params;
      return validationHelpers.checkoutSear(params);
    },
    // 处理结果 - 核心方法，每次值变化都会调用
    result(val) {
      const paramsData = val || this.data.params;
      const isHeight = this._getHeightStatus(paramsData);
      const processedData = dataHelpers.handleData(paramsData);
      // console.log('paramsData', paramsData);
      // console.log('isHeight', isHeight);
      // console.log('处理后的数据processedData', processedData);

      this.setData({
        paramsData: processedData,
        isHeight
      });
      return;

      // 触发事件，传递给父组件
      this.triggerEvent('submit', {
        isHeight,
        paramsData: dataHelpers.deepClone(processedData)
      });
    },

    /**
     * 获取高亮状态 - 判断是否有选中项
     */
    _getHeightStatus(paramsData) {
      let isHeight = false;

      Object.keys(paramsData).some(key => {
        // 处理自定义输入的情况
        if (SEARCH_CONSTANTS.RANGE_INPUT_FIELDS.includes(key)) {
          if (
            paramsData[key].length > 0 &&
            (paramsData[key][0]?.start || paramsData[key][0]?.end)
          ) {
            isHeight = true;
            return true;
          }
        } else if (SEARCH_CONSTANTS.INPUT_FIELDS.includes(key)) {
          if (paramsData[key] && paramsData[key].trim().length > 0) {
            isHeight = true;
            return true;
          }
        } else if (paramsData[key] && paramsData[key].length > 0) {
          isHeight = true;
          return true;
        }
      });

      return isHeight;
    },

    //   弹窗选择回调 - 使用 PopupManager 处理
    submitSub(e) {
      const {
        itemList
      } = this.data;
      let {
        checkedList: obj,
        mark
      } = e.detail;

      // 使用 PopupManager 获取配置
      const popupConfig = PopupManager.getPopupConfig(mark);
      if (!popupConfig) return;

      // 获取显示名称
      const name = formatHelpers.formatOptions(
        handleMultiple(obj)
      );
      // 更新itemList中对应项的content
      itemList.forEach(item => {
        if (item.type === mark) {
          item.content = name;
        }
      });

      // 构建更新数据 - 使用 PopupManager 关闭所有弹窗
      const updateData = {
        [`params.${popupConfig.mark}`]: obj,
        itemList,
        ...PopupManager.closeAllPopups()
      };

      componentHelpers.updateComponentState(this, updateData, () => {
        this.result();
      });
    },

    /**
     * 外部调用方法 - 使用组件工具
     */
    clearChildComponent() {
      this.clearSear();
    },

    /**
     * 设置回填数据 - 完整实现，使用工具模块
     */
    setBackfillData(tempObj = {}) {
      this.clearSear();

      let {
        itemList,
        params
      } = this.data;
      let {
        minCapital,
        maxCapital,
        capitalActive,
        socialActive,
        socialminPeson,
        socialmaxPeson,
        minDate,
        maxDate,
        dateActive
      } = this.data;

      // 合并参数
      params = Object.assign(params, tempObj);

      // 处理itemList回填
      itemList = itemList.map(item => {
        for (let key in params) {
          if (item.type === key) {
            let tagId = '';

            // 根据数据类型设置对应的tagId
            switch (item.type) {
              case 'reg_capital': // 注册资本
                let strs = params[key].length ?
                  params[key][0].start + '$' + params[key][0].end :
                  '';
                let tagIdArr = [
                  '0$100',
                  '100$200',
                  '500$1000',
                  '1000$5000',
                  '5000$'
                ];

                if (!tagIdArr.includes(strs)) {
                  minCapital = params[key].length ? params[key][0].start : '';
                  maxCapital = params[key].length ? params[key][0].end : '';
                  if (minCapital || maxCapital) capitalActive = true;
                  tagId = strs;
                } else {
                  tagId = params[key];
                }
                break;

              case 'insured_num': // 从业人数
                let strss = params[key].length ?
                  params[key][0].start + '$' + params[key][0].end :
                  '';
                let tagIdArrs = [
                  '0$49',
                  '50$99',
                  '100$499',
                  '500$999',
                  '1000$4999',
                  '5000$'
                ];

                if (!tagIdArrs.includes(strss)) {
                  socialminPeson = params[key].length ?
                    params[key][0].start :
                    '';
                  socialmaxPeson = params[key].length ? params[key][0].end : '';
                  if (socialminPeson || socialmaxPeson) socialActive = true;
                  tagId = strss;
                } else {
                  tagId = params[key];
                }
                break;

              case 'est_date': // 注册时间
                let str = params[key].length ?
                  params[key][0].start + '$' + params[key][0].end :
                  '';
                const dateTagList = this._getDateTagList();

                if (!dateTagList.includes(str)) {
                  minDate = params[key].length ? params[key][0].start : '';
                  maxDate = params[key].length ? params[key][0].end : '';
                  if (minDate || maxDate) dateActive = true;
                  tagId = str;
                } else {
                  tagId = params[key];
                }
                break;

              default:
                tagId = params[key];
                break;
            }

            // 设置弹窗类型字段的content
            const popupFields = [
              'areas',
              'trade_types',
              'ent_type',
              'enterprise_license_list'
            ];
            const popupFieldMapping = {
              areas: 'area_code_list',
              trade_types: 'industry_code_list',
              ent_type: 'ent_type',
              enterprise_license_list: 'enterprise_license_list'
            };

            if (popupFields.includes(key)) {
              const dataField = popupFieldMapping[key];
              const obj = params[dataField];
              if (obj?.length > 0) {
                item.content = formatHelpers.formatOptions(
                  obj.filter(item => item.status === 'checked')
                );
              }
            } else if (item.list) {
              // 处理标签选择类型
              item.list = item.list.map(tag => {
                const isArray = Array.isArray(tagId);
                if (isArray) {
                  for (let number of tagId) {
                    if (tag.id === number) {
                      tag.active = true;
                    }
                  }
                } else {
                  if (tag.id === tagId) {
                    tag.active = true;
                  }
                }
                return tag;
              });
            }
          }
        }
        return item;
      });

      componentHelpers.updateComponentState(this, {
        params,
        itemList,
        minCapital,
        maxCapital,
        capitalActive,
        socialActive,
        socialminPeson,
        socialmaxPeson,
        minDate,
        maxDate,
        dateActive
      });
    },

    /**
     * 获取日期标签列表
     */
    _getDateTagList() {
      return [
        '2024$2025',
        '2023$2024',
        '2022$2023',
        '2021$2022',
        '2020$2021',
        '2019$2020',
        '2018$2019',
        '2017$2018',
        '2016$2017',
        '2015$2016',
        '2010$2015',
        '2005$2010',
        '2000$2005',
        '$2000'
      ];
    },

    fillChildComponent(data) {
      this.setBackfillData(data);
    },

    closeEntNamePop() {
      this.closeInptPop();
    },

    getProcessedData() {
      const processedData = dataHelpers.handleData(this.data.params);
      return dataHelpers.handlestructure(processedData);
    },

    getComponentStatus() {
      return {
        isHeight: this.data.isHeight,
        params: this.data.params,
        paramsData: this.data.paramsData,
        variant: this.data.variant
      };
    },

    /**
     * 输入框处理方法 - 使用工具模块
     */
    inputFocus(e) {
      const type = e.currentTarget.dataset.type;
      this._setTagStatus(type);
    },

    inputChange(e) {
      const type = e.currentTarget.dataset.type;
      const {
        minCapital,
        maxCapital,
        socialmaxPeson,
        socialminPeson
      } =
      this.data;

      if (type === 'insured_num') {
        componentHelpers.updateComponentState(
          this, {
            socialActive: socialminPeson !== '' || socialmaxPeson !== '',
            [`params.${type}`]: [{
              start: socialminPeson,
              end: socialmaxPeson,
              special: true
            }]
          },
          () => this.result()
        );
      } else if (type === 'reg_capital') {
        componentHelpers.updateComponentState(
          this, {
            capitalActive: minCapital !== '' || maxCapital !== '',
            [`params.${type}`]: [{
              start: minCapital,
              end: maxCapital,
              special: true
            }]
          },
          () => this.result()
        );
      }
    },

    _setTagStatus(type) {
      let {
        itemList,
        params
      } = this.data;

      for (let item of itemList) {
        if (item.type === type && item.list) {
          item.list = item.list.map(tag => {
            tag.active = false;
            return tag;
          });
        }
      }

      params[type] = [];
      componentHelpers.updateComponentState(this, {
        itemList,
        params
      });
    },

    //   日期选择处理
    setDate(e) {
      const {
        date
      } = e.detail;
      let {
        minDate,
        maxDate,
        dateType
      } = this.data;
      console.log('1111', date);
      if (date) {
        if (dateType === 'startDate') minDate = date;
        if (dateType === 'endDate') maxDate = date;
      }
      let name = '';
      if (minDate) name = '最低年限' + minDate;
      if (maxDate) name += '最高年限' + maxDate;
      if (minDate || maxDate) {
        this._setTagStatus('est_date');
      }
      componentHelpers.updateComponentState(
        this, {
          minDate,
          maxDate,
          dateActive: minDate || maxDate,
          'params.est_date': [{
            start: minDate,
            end: maxDate,
            special: true,
            name
          }]
        },
        () => this.result()
      );
    },

    showtDatePicker(e) {
      const type = e.currentTarget.dataset.type;
      let {
        date,
        minDate,
        maxDate
      } = this.data;

      date = type === 'startDate' ? minDate : maxDate;
      this.setData({
        datePop: true,
        dateType: type,
        date,
        dateTitle: type === 'startDate' ? '最低年限' : '最高年限'
      });
    }
  }
});