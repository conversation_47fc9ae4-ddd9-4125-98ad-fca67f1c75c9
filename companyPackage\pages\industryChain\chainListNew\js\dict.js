// 时间戳转日期
function transformDate(num) {
  let number = new Date(num);
  let year = number.getFullYear();
  let month = number.getMonth() + 1;
  let day = number.getDate();
  let date = year + '-' + month + '-' + day;
  return date;
}
// 计算日期
function calcDate(start, end) {
  let curDate = new Date().getTime(); // 当前时间的时间戳
  let oneYear = 365 * 24 * 60 * 60 * 1000; // 一年时间的时间戳
  let startDate = curDate - end * oneYear; // 开始时间
  let endDate = curDate - start * oneYear; // 结束时间
  let dateCombination =
    (end ? transformDate(startDate) : '') + '$' + transformDate(endDate);
  return dateCombination;
}
// 注册时间
export const REG_TIME_ENUM = [{
    code: 1,
    id: calcDate(0, 1),
    text: '1年内'
  },
  {
    code: 2,
    id: calcDate(1, 3),
    text: '1-3年'
  },
  {
    code: 3,
    id: calcDate(3, 5),
    text: '3-5年'
  },
  {
    code: 4,
    id: calcDate(5, 10),
    text: '5-10年'
  },
  {
    code: 5,
    id: calcDate(10, 0),
    text: '10年以上'
  }
];
// 企业规模
export const ent_size_ENUM = [{
    id: 'S',
    name: 'S'
  },
  {
    name: 'A+',
    id: 'APLUS'
  },
  {
    id: 'A',
    name: 'A'
  },
  {
    name: 'B+',
    id: 'BPLUS'
  },
  {
    id: 'C+',
    name: 'C'
  },
  {
    id: 'D',
    name: 'D'
  },
  {
    name: 'E+',
    id: 'EPLUS'
  },
  {
    id: 'E',
    name: 'E'
  }
];
// 效益评估
export const benefits_assess_ENUM = [{
    id: 'S',
    name: 'S'
  },
  {
    name: 'A+',
    id: 'APLUS'
  },
  {
    id: 'A',
    name: 'A'
  },
  {
    name: 'B+',
    id: 'BPLUS'
  },
  {
    id: 'C+',
    name: 'C'
  },
  {
    id: 'D',
    name: 'D'
  },
  {
    name: 'E+',
    id: 'EPLUS'
  },
  {
    id: 'E',
    name: 'E'
  }
];