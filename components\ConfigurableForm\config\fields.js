/**
 * 字段配置管理模块
 * 统一管理表单字段配置，支持动态字段控制
 */

/**
 * 字段类型枚举
 */
const FIELD_TYPES = {
  INPUT: 'input',
  SELECT: 'select',
  POPUP: 'popup',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',
  RANGE: 'range',
  DATE: 'date'
};

/**
 * 字段特殊类型枚举
 */
const SPECIAL_TYPES = {
  INPUT: 'input',
  POP: 'pop',
  TPOP: 'tpop'
};

// 搜索常量配置

const SEARCH_CONSTANTS = {
  // 单选字段列表
  RADIO_FIELDS: [
    'fixed_phone_flag',
    'mobile_phone_flag',
    'email_flag',
    'tax_credit_flag',
    'trade_mark_info_flag',
    'android_app',
    'apple_app',
    'applet',
    'we_chat_official_account',
    'weibo_flag',
    'dishonest_info',
    'judgment_doc',
    'administrative_penalty',
    'chattel_mortgage',
    'business_abnormalities',
    'tendering_and_bidding',
    'job_flag'
  ],

  // 多选字段列表
  MULTI_SELECT_FIELDS: [
    'technology',
    'ent_size',
    'ent_status_list',
    'ent_entity_type',
    'patent_info',
    'financing_info_list',
    'insured_num'
  ],

  // 范围输入字段列表
  RANGE_INPUT_FIELDS: ['reg_capital', 'est_date', 'insured_num'],

  // 弹窗选择字段列表
  POPUP_FIELDS: [
    'areas',
    'trade_types',
    'ent_type',
    'enterprise_license_list',
    'industrial_list',
    'classic_industry_code_list'
  ],

  // 输入框字段列表
  INPUT_FIELDS: ['ent_name'],

  // 布尔值字段列表
  BOOL_FIELDS: ['fixed_phone_flag', 'mobile_phone_flag', 'email_flag']
};

/**
 * 弹窗字段映射配置
 */
const POPUP_FIELD_MAPPING = {
  areas: {
    data: 'regionData',
    mark: 'areas',
    dataType: 'districtAry',
    popupKey: 'regionPop',
    title: '选择地区'
  },
  trade_types: {
    data: 'eleseic_data',
    mark: 'trade_types',
    dataType: 'eleseicAry',
    popupKey: 'eleseicPop',
    title: '选择行业'
  },
  ent_type: {
    data: 'enttype_data',
    mark: 'ent_type',
    dataType: 'enttypeAry',
    popupKey: 'enttypePop',
    title: '选择企业类型'
  },
  enterprise_license_list: {
    data: 'all_cert_data',
    mark: 'enterprise_license_list',
    dataType: 'allCertAry',
    popupKey: 'districtPop',
    title: '选择企业许可'
  },
  industrial_list: {
    data: 'industrial_data',
    mark: 'industrial_list',
    dataType: 'industrialAry',
    popupKey: 'industrialPop',
    title: '选择产业链'
  },
  classic_industry_code_list: {
    data: 'classic_industry_data',
    mark: 'classic_industry_code_list',
    dataType: 'classicIndustryAry',
    popupKey: 'classicIndustryPop',
    title: '选择经典行业'
  }
};

/**
 * 弹窗配置列表 - 用于模板循环渲染
 */
const POPUP_CONFIGS = [
  {
    type: 'areas',
    visible: 'regionPop',
    mark: 'areas',
    dataType: 'districtAry',
    oldDataKey: 'regionData',
    title: '选择地区'
  },
  {
    type: 'trade_types',
    visible: 'eleseicPop',
    mark: 'trade_types',
    dataType: 'eleseicAry',
    oldDataKey: 'eleseic_data',
    title: '选择行业'
  },
  {
    type: 'ent_type',
    visible: 'enttypePop',
    mark: 'ent_type',
    dataType: 'enttypeAry',
    oldDataKey: 'enttype_data',
    title: '选择企业类型'
  },
  {
    type: 'enterprise_license_list',
    visible: 'districtPop',
    mark: 'enterprise_license_list',
    dataType: 'allCertAry',
    oldDataKey: 'all_cert_data',
    title: '选择企业许可'
  }
];

/**
 * 范围输入字段提示配置
 */
const RANGE_INPUT_TIPS = {
  reg_capital: {
    min: '最低资本',
    max: '最高资本'
  },
  est_date: {
    min: '最低年限',
    max: '最高年限'
  },
  insured_num: {
    min: '最少人数',
    max: '最多人数'
  }
};

/**
 * 字段验证规则配置
 */
const FIELD_VALIDATION_RULES = {
  ent_name: {
    maxLength: 24,
    required: false,
    placeholder: '请输入企业名称(非必填)'
  },
  reg_capital: {
    type: 'number',
    min: 0,
    required: false
  },
  est_date: {
    type: 'date',
    required: false
  },
  insured_num: {
    type: 'number',
    min: 0,
    required: false
  }
};

/**
 * 字段显示条件配置
 */
const FIELD_DISPLAY_CONDITIONS = {
  // 根据组件变体显示/隐藏字段
  variant: {
    simplified: {
      exclude: ['chain_codes'] // 简化版排除的字段
    },
    full: {
      exclude: [] // 完整版不排除任何字段
    }
  },

  // 根据VIP状态显示/隐藏字段
  vip: {
    required: [
      'ent_size',
      'technology',
      'enterprise_license_list',
      'mobile_phone_flag',
      'financing_info_list',
      'tendering_and_bidding',
      'job_flag',
      'tax_credit_flag',
      'trade_mark_info_flag',
      'patent_info',
      'dishonest_info',
      'judgment_doc',
      'administrative_penalty'
    ]
  }
};

/**
 * 获取字段配置
 * @param {string} fieldType - 字段类型
 * @param {Object} options - 配置选项
 * @returns {Object} 字段配置对象
 */
function getFieldConfig(fieldType, options) {
  options = options || {};

  const baseConfig = Object.assign(
    {
      type: fieldType,
      required: false,
      visible: true
    },
    options
  );

  // 根据字段类型设置特定配置
  if (SEARCH_CONSTANTS.RADIO_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'radio';
    baseConfig.multiple = false;
  } else if (SEARCH_CONSTANTS.MULTI_SELECT_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'checkbox';
    baseConfig.multiple = true;
  } else if (SEARCH_CONSTANTS.RANGE_INPUT_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'range';
    baseConfig.tips = RANGE_INPUT_TIPS[fieldType];
  } else if (SEARCH_CONSTANTS.POPUP_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'popup';
    baseConfig.popupConfig = POPUP_FIELD_MAPPING[fieldType];
  } else if (SEARCH_CONSTANTS.INPUT_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'input';
    baseConfig.validation = FIELD_VALIDATION_RULES[fieldType];
  }

  return baseConfig;
}

/**
 * 检查字段是否应该显示
 * @param {string} fieldType - 字段类型
 * @param {Object} context - 上下文信息
 * @returns {boolean} 是否显示
 */
function shouldShowField(fieldType, context) {
  context = context || {};
  const variant = context.variant || 'full';
  const vipStatus = context.vipStatus || false;

  // 检查变体排除条件
  const variantConfig = FIELD_DISPLAY_CONDITIONS.variant[variant];
  const variantExcludes = variantConfig ? variantConfig.exclude : [];
  if (variantExcludes.includes(fieldType)) {
    return false;
  }

  // 检查VIP条件
  const vipRequired = FIELD_DISPLAY_CONDITIONS.vip.required.includes(fieldType);
  if (vipRequired && !vipStatus) {
    // 可以选择隐藏或显示但禁用，这里选择显示但标记为VIP
    return true;
  }

  return true;
}

/**
 * 获取字段验证规则
 * @param {string} fieldType - 字段类型
 * @returns {Object} 验证规则
 */
function getFieldValidation(fieldType) {
  return FIELD_VALIDATION_RULES[fieldType] || {};
}

/**
 * 生成字段配置列表
 * @param {Array} fieldList - 字段列表
 * @param {Object} context - 上下文信息
 * @returns {Array} 配置后的字段列表
 */
function generateFieldConfigs(fieldList, context) {
  context = context || {};

  return fieldList
    .filter(function (field) {
      return shouldShowField(field.type, context);
    })
    .map(function (field) {
      return Object.assign({}, field, {
        config: getFieldConfig(field.type),
        validation: getFieldValidation(field.type)
      });
    });
}

// 导出模块
module.exports = {
  FIELD_TYPES: FIELD_TYPES,
  SPECIAL_TYPES: SPECIAL_TYPES,
  SEARCH_CONSTANTS: SEARCH_CONSTANTS,
  POPUP_FIELD_MAPPING: POPUP_FIELD_MAPPING,
  POPUP_CONFIGS: POPUP_CONFIGS,
  RANGE_INPUT_TIPS: RANGE_INPUT_TIPS,
  FIELD_VALIDATION_RULES: FIELD_VALIDATION_RULES,
  FIELD_DISPLAY_CONDITIONS: FIELD_DISPLAY_CONDITIONS,
  getFieldConfig: getFieldConfig,
  shouldShowField: shouldShowField,
  getFieldValidation: getFieldValidation,
  generateFieldConfigs: generateFieldConfigs
};
