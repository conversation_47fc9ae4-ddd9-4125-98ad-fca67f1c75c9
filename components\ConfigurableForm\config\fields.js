import constant from '../../../utils/constant';
/**
 * 字段类型枚举
 */
const FIELD_TYPES = {
  INPUT: 'input',
  SELECT: 'select',
  POPUP: 'popup',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',
  RANGE: 'range',
  DATE: 'date'
};

/**
 * 字段特殊类型枚举
 */
const SPECIAL_TYPES = {
  INPUT: 'input',
  POP: 'pop',
  TPOP: 'tpop'
};

// 搜索常量配置

const SEARCH_CONSTANTS = {
  // 单选字段列表
  RADIO_FIELDS: [
    'copyright_of_works',
    'official_website_info',
    'icp_filing',
    'android_app',
    'apple_app',
    'applet',
    'we_chat_official_account',
    'weibo_flag',
    'dishonest_info',
    'judgment_doc',
    'administrative_penalty',
    'chattel_mortgage',
    'business_abnormalities',
    'copyrights_flag',
    'trade_mark_info_flag',
    'tax_credit_flag',
    'job_flag',
    'tendering_and_bidding',
    'head_ent_flag',
    'fixed_phone_flag',
    'mobile_phone_flag',
    'email_flag'
  ],

  // 多选字段列表
  MULTI_SELECT_FIELDS: [
    'ent_expand',
    'ent_size',
    'benefits_assess',
    'technology',
    'ent_status_list',
    'ent_entity_type',
    'financing_info_list',
    'listing_status',
    'patent_info'
  ],

  // 范围输入字段列表 & insured_num就先放到这里 
  RANGE_INPUT_FIELDS: ['est_date', 'reg_capital', 'insured_num'],

  // 弹窗选择字段列表
  POPUP_FIELDS: [
    'area_code_list',
    'industry_code_list',
    'ent_type',
    'enterprise_license_list',
    'industrial_list' // 弹窗-多选
  ],

  // 输入框字段列表
  INPUT_FIELDS: ['ent_name'],

  // 布尔值字段列表
  BOOL_FIELDS: []
};

/**
 * 弹窗字段映射配置
 */
const POPUP_FIELD_MAPPING = {
  area_code_list: {
    mark: 'area_code_list',
    dataType: constant.DistrictAry,
    popupKey: 'area_code_list_pop',
    title: '选择地区'
  },
  industry_code_list: {
    mark: 'industry_code_list', // 标识字段
    dataType: constant.EleseicAry, // 数据源
    popupKey: 'industry_code_list_pop',
    title: '选择行业'
  },
  ent_type: {
    mark: 'ent_type',
    dataType: constant.EnttypeAry,
    popupKey: 'ent_type_pop',
    title: '选择企业类型'
  },
  enterprise_license_list: {
    mark: 'enterprise_license_list',
    dataType: constant.AllCertAry,
    popupKey: 'enterprise_license_list_pop',
    title: '选择企业许可'
  },
  industrial_list: {
    mark: 'industrial_list',
    dataType: constant.Industrial,
    popupKey: 'industrial_list_pop',
    title: '选择产业链'
  }
};

/**
 * 弹窗配置列表 - 用于模板循环渲染
 */
const POPUP_CONFIGS = [{
    type: 'area_code_list',
    visible: 'area_code_list_pop',
    mark: 'area_code_list',
    dataType: 'districtAry',
    oldDataKey: 'area_code_list',
    title: '选择地区',
    component: 'multiplec-choice'
  },
  {
    type: 'industry_code_list',
    visible: 'industry_code_list_pop',
    mark: 'industry_code_list',
    dataType: 'eleseicAry',
    oldDataKey: 'industry_code_list',
    title: '选择行业',
    component: 'multiplec-choice'
  },
  {
    type: 'ent_type',
    visible: 'ent_type_pop',
    mark: 'ent_type',
    dataType: 'enttypeAry',
    oldDataKey: 'ent_type',
    title: '选择企业类型',
    component: 'multiplec-choice'
  },
  {
    type: 'enterprise_license_list',
    visible: 'enterprise_license_list_pop',
    mark: 'enterprise_license_list',
    dataType: 'allCertAry',
    oldDataKey: 'enterprise_license_list',
    title: '选择企业许可',
    component: 'multiplec-choice'
  },
  {
    type: 'industrial_list',
    visible: 'industrial_list_pop',
    mark: 'industrial_list',
    dataType: 'Industrial',
    oldDataKey: 'industrial_list',
    title: '选择产业链',
    component: 'BtmListMultGroupPop'
  }
];

/**
 * 范围输入字段提示配置
 */
const RANGE_INPUT_TIPS = {
  reg_capital: {
    min: '最低资本',
    max: '最高资本'
  },
  est_date: {
    min: '最低年限',
    max: '最高年限'
  },
  // insured_num: {
  //   min: '最少人数',
  //   max: '最多人数'
  // }
};

/**
 * 字段验证规则配置
 */
const FIELD_VALIDATION_RULES = {
  ent_name: {
    maxLength: 24,
    required: false,
    placeholder: '请输入企业名称(非必填)'
  },
  reg_capital: {
    type: 'number',
    min: 0,
    required: false
  },
  est_date: {
    type: 'date',
    required: false
  },
  insured_num: {
    type: 'number',
    min: 0,
    required: false
  }
};

/**
 * 字段显示条件配置
 */
const FIELD_DISPLAY_CONDITIONS = {
  // 根据组件变体显示/隐藏字段
  variant: {
    simplified: {
      exclude: ['chain_codes'] // 简化版排除的字段
    },
    full: {
      exclude: [] // 完整版不排除任何字段
    }
  },

  // 根据VIP状态显示/隐藏字段
  vip: {
    required: [
      'ent_size',
      'technology',
      'enterprise_license_list',
      'mobile_phone_flag',
      'financing_info_list',
      'tendering_and_bidding',
      'job_flag',
      'tax_credit_flag',
      'trade_mark_info_flag',
      'patent_info',
      'dishonest_info',
      'judgment_doc',
      'administrative_penalty'
    ]
  }
};

/**
 * 获取字段配置
 * @param {string} fieldType - 字段类型
 * @param {Object} options - 配置选项
 * @returns {Object} 字段配置对象
 */
function getFieldConfig(fieldType, options) {
  options = options || {};

  const baseConfig = Object.assign({
      type: fieldType,
      required: false,
      visible: true
    },
    options
  );

  // 根据字段类型设置特定配置
  if (SEARCH_CONSTANTS.RADIO_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'radio';
    baseConfig.multiple = false;
  } else if (SEARCH_CONSTANTS.MULTI_SELECT_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'checkbox';
    baseConfig.multiple = true;
  } else if (SEARCH_CONSTANTS.RANGE_INPUT_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'range';
    baseConfig.tips = RANGE_INPUT_TIPS[fieldType];
  } else if (SEARCH_CONSTANTS.POPUP_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'popup';
    baseConfig.popupConfig = POPUP_FIELD_MAPPING[fieldType];
  } else if (SEARCH_CONSTANTS.INPUT_FIELDS.includes(fieldType)) {
    baseConfig.inputType = 'input';
    baseConfig.validation = FIELD_VALIDATION_RULES[fieldType];
  }

  return baseConfig;
}

/**
 * 检查字段是否应该显示
 * @param {string} fieldType - 字段类型
 * @param {Object} context - 上下文信息
 * @returns {boolean} 是否显示
 */
function shouldShowField(fieldType, context) {
  context = context || {};
  const variant = context.variant || 'full';
  const vipStatus = context.vipStatus || false;

  // 检查变体排除条件
  const variantConfig = FIELD_DISPLAY_CONDITIONS.variant[variant];
  const variantExcludes = variantConfig ? variantConfig.exclude : [];
  if (variantExcludes.includes(fieldType)) {
    return false;
  }

  // 检查VIP条件
  const vipRequired = FIELD_DISPLAY_CONDITIONS.vip.required.includes(fieldType);
  if (vipRequired && !vipStatus) {
    // 可以选择隐藏或显示但禁用，这里选择显示但标记为VIP
    return true;
  }

  return true;
}

/**
 * 获取字段验证规则
 * @param {string} fieldType - 字段类型
 * @returns {Object} 验证规则
 */
function getFieldValidation(fieldType) {
  return FIELD_VALIDATION_RULES[fieldType] || {};
}

/**
 * 生成字段配置列表
 * @param {Array} fieldList - 字段列表
 * @param {Object} context - 上下文信息
 * @returns {Array} 配置后的字段列表
 */
function generateFieldConfigs(fieldList, context) {
  context = context || {};

  return fieldList
    .filter(function (field) {
      return shouldShowField(field.type, context);
    })
    .map(function (field) {
      return Object.assign({}, field, {
        config: getFieldConfig(field.type),
        validation: getFieldValidation(field.type)
      });
    });
}

// 导出模块
module.exports = {
  FIELD_TYPES: FIELD_TYPES,
  SPECIAL_TYPES: SPECIAL_TYPES,
  SEARCH_CONSTANTS: SEARCH_CONSTANTS,
  POPUP_FIELD_MAPPING: POPUP_FIELD_MAPPING,
  POPUP_CONFIGS: POPUP_CONFIGS,
  RANGE_INPUT_TIPS: RANGE_INPUT_TIPS,
  FIELD_VALIDATION_RULES: FIELD_VALIDATION_RULES,
  FIELD_DISPLAY_CONDITIONS: FIELD_DISPLAY_CONDITIONS,
  getFieldConfig: getFieldConfig,
  shouldShowField: shouldShowField,
  getFieldValidation: getFieldValidation,
  generateFieldConfigs: generateFieldConfigs
};