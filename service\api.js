import request from './request'
import {
  serializeObj
} from '../utils/util'
import {
  userUrl,
  websiteUrl,
  chainUrl,
  monitorUrl,
  // newChainUrl,
  mapUrl,
  dshujuRrl,
  smsUrl,
  newMonitorUrl,
  enterpriseUrl,
  OderUrl,
  newIndustryUrl
} from './config'
import invoiceInfo from "./invoiceInfo";
import industrialEvaluationAPI from './industrialEvaluationAPI'
import payAPI from './pay'
// 根据模块封装接口
module.exports = {
  user: {
    // 用户手机号码解密
    phoneDecrypt(data) {
      return request({
        url: `${websiteUrl}/wechat/decrypt`,
        method: 'POST',
        data,
      })
    },
    // 获取token
    getTokens(data) {
      return request({
        url: `${userUrl}/tokens/phone/only`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    register(data) {
      return request({
        url: `${userUrl}/customer/token/wechat/register`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    login(data) {
      return request({
        url: `${userUrl}/tokens/wechat`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    privileges() {
      return request({
        url: `${userUrl}/customer/privilege/data`,
        method: 'GET',
      })
    },
    priviFormate() {
      //我的权限列表(格式化)
      return request({
        url: `${userUrl}/privileges/detail/user/my`,
        method: 'GET',
      })
    },
    openId() {
      //获取openId
      let user_id = wx.getStorageSync('TOKEN')['user_id']
      return request({
        url: `${userUrl}/users/${user_id}/detail`,
        method: 'GET',
      })
    },
    // 22.11.03
    newGetTokens(data) {
      return request({
        url: `${userUrl}/customer/token`,
        method: 'POST',
        data,
      })
    },
    newGetUserInfo(id) {
      return request({
        url: `${userUrl}/users/${id}/detail`,
        method: 'GET',
      })
    },
    getCode(data) {
      return request({
        url: `${smsUrl}/verification_code`,
        method: 'POST',
        data,
        hideLoading: true,
      })
    },
    // 验证码登录
    verifyCodeLogin(data) {
      return request({
        url: `${userUrl}/customer/token/registerByCode`,
        method: 'POST',
        data,
        hideLoading: true,
      })
    },
    // 手机号密码-登录
    phonePas(data) {
      return request({
        url: `${userUrl}/tokens/phone`,
        method: 'POST',
        data,
        hideLoading: true,
      })
    },
    // 手机号密码-登录
    wxLogin(data) {
      return request({
        url: `${userUrl}/tokens/wechat/decrypt/phone`,
        method: 'POST',
        data,
        hideLoading: true,
      })
    },
    // 手机号登录
    phoneLogin(data) {
      return request({
        url: `${userUrl}/tokens/phone/only`,
        method: 'POST',
        data,
        hideLoading: true,
      })
    },
    //修改昵称
    reviseNickName(data) {
      return request({
        url: `${userUrl}/users/only/user`,
        method: 'put',
        data,
      })
    },
    updatePassword(data) {
      return request({
        url: `${userUrl}/members/password/reset/code`,
        method: 'put',
        data,
      })
    },
    updatePhone(data) {
      return request({
        url: `${userUrl}/members/reset/phone`,
        method: 'put',
        data,
      })
    },
    updatePhoto(data) {
      return request({
        url: `${userUrl}/customer/updatePersonHeadSculpture`,
        method: 'post',
        data,
      })
    },
    accountCanReason() {
      return request({
        url: `${userUrl}/customer/logout/enumData`,
        method: 'GET',
      })
    },
    logoutApply(data) {
      return request({
        url: `${userUrl}/customer/logout/apply`,
        method: 'post',
        data,
      })
    },
    // 用户团队信息 
    userTeam(user_id) {
      return request({
        url: `${userUrl}/customer/${user_id}/my`,
        method: 'get'
      })
    },
    tokenChange(data) {
      return request({
        url: `${userUrl}/tokens/change`,
        method: 'put',
        data
      })
    },
    // 切换账号获取消息数量 
    statisticsMessageCount(data) {
      return request({
        url: `${smsUrl}/message/statisticsMessageCount`,
        method: 'post',
        data
      })
    },
    updateNiceName() {
      return request({
        url: `${enterpriseUrl}/project/management/update/nick/name`,
        method: 'put',
      })
    }
  },
  // 产业链
  chain: {
    // 热门产业链
    chainHot() {
      return request({
        url: `${chainUrl}/merchants/industry/hot`,
        method: 'GET',
      })
    },
    // 我的产业链
    chainMy() {
      return request({
        url: `${chainUrl}/industry/chain/mine`,
        method: 'GET',
      })
    },
    // 所有产业链
    chainAll() {
      return request({
        url: `${chainUrl}/merchants/industry/treeNode/app`,
        method: 'GET',
      })
    },
    // 新兴产业  
    xinChainAll() {
      return request({
        url: `${newMonitorUrl}/industry/treeNode`,
        method: 'GET',
      })
    },
    // 产业链下拉
    getChains(code, data) {
      //改版data为原来的地区编码
      return request({
        url: `${chainUrl}/industry/chain/code/${code}/plus/tag?area_code=${data}`,
        method: 'GET',
      })
    },
    // 产业链详情
    chainDetail(data) {
      return request({
        url: `${chainUrl}/industry`,
        method: 'POST',
        data,
      })
    },
    // 产业链详情
    chainList(data) {
      return request({
        url: `${enterpriseUrl}/project/management/industry`,
        method: 'POST',
        data,
      })
    },
  },
  // 地图招商
  map: {
    // 热力图--被淘汰了 后续没用就可以删了
    mapHot(data) {
      return request({
        url: `${mapUrl}/maps/hot`,
        method: 'POST',
        data,
      })
    },
    // 企业列表
    mapList(data) {
      return request({
        url: `${mapUrl}/maps`,
        method: 'POST',
        data,
      })
    },
    // 区县热力图 
    mapHots(data) {
      return request({
        url: `${newIndustryUrl}/maps/entityDistribute`,
        method: 'POST',
        data,
      })
    },
  },

  // 公共接口
  common: {
    sigin(ent_id, level) { // 获取sigin 跳转详情需要
      let str = `${chainUrl}/search/new_h5_report/sign/${ent_id}?level=${level ? level : 'NONE'}`
      return request({
        url: str,
        method: 'GET',
      })
    },
    perSigin(personid, level) { // 获取sigin 跳转详情需要
      let str = `${chainUrl}/search/new_h5_report/search_boss_sign?personid=${personid}&level=${level ? level : 'NONE'}`
      return request({
        url: str,
        method: 'GET',
      })
    },

    // 
    definitiveThemeTypeDict() { // 权威榜单-榜单类型字典数据
      return request({
        url: `${chainUrl}/list/dictionaryNewData`,
        method: 'GET',
      })
    },
    // 地区从查询
    getDistrict(data) {
      return request({
        url: `${websiteUrl}/ent/district?${serializeObj(data)}`,
        method: 'GET',
      })
    },
    // 针对商机的地区
    getDis(data) {
      return request({
        url: `${websiteUrl}/business/region?${serializeObj(data)}`,
        method: 'GET',
      })
    },
    // 收藏企业到项目管理
    collectCompanytoProject(data) {
      return request({
        url: `${enterpriseUrl}/project/management`,
        method: 'POST',
        data,
      })
    },
    collectCompany(data) {
      return request({
        url: `${enterpriseUrl}/project/management`,
        method: 'POST',
        data,
      })
    },
    // 验证成功取消收藏企业
    cancelCollect(data) {
      let user_id = JSON.parse(wx.getStorageSync('user-info'))['user_id']
      return request({
        url: `${enterpriseUrl}/project/management`,
        method: 'DELETE',
        data: [{
          ent_id: data.ent_id,
          track_user_id: user_id
        }],
      })
    },
    getSerDistrict(data) {
      return request({
        url: `${chainUrl}/search/district`,
        method: 'POST',
        data,
      })
    },
    getXNYQC(data) {
      return request({
        url: `${chainUrl}/industry/address/chain/count`,
        method: 'POST',
        data,
      })
    },
    // 企业名字查询
    getNameList(name) {
      //data 为String
      return request({
        url: `${chainUrl}/industry/ent/name?ent_name=${encodeURI(name)}`,
        method: 'GET',
      })
    },
    //查询行业列表
    trade(data) {
      return request({
        url: `${websiteUrl}/ent/trade`,
        method: 'POST',
        data,
      })
    },
    //查询企业联系方式
    contact(data, type) {
      return request({
        url: `${chainUrl}/industry/ent/contact/${data}?${type}`,
        method: 'GET',
        // data:type,
      })
    },
    // 查询企业地址
    getComAddress(data) {
      return request({
        url: `${chainUrl}/search/ent/detail/${data}`,
        method: 'POST',
      })
    },
    // 22.1.5最新获取---全量行业树
    eleseicPlus(data) {
      //[code1,code2,code3]
      // return request({
      //   url: `${websiteUrl}/ent/elseic_plus`,
      //   method: 'POST',
      //   data
      // })
      return new Promise((res, rej) => {
        wx.request({
          url: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/elseic_plus.json',
          headers: {
            'Content-Type': 'application/json',
          },
          success: (r) => {
            if (r.statusCode === 200) {
              res(r.data.data)
            }
          },
        })
      })
    },
    // 22.1.5--全部企业许可
    getAllCert(data) {
      //[code1,code2,code3]
      return request({
        url: `${chainUrl}/search/all_cert`,
        method: 'POST',
        data,
      })
    },
    // 22.1.5--全部企业类型
    allEntType(data) {
      //[code1,code2,code3]
      return request({
        url: `${chainUrl}/search/ent_type`,
        method: 'POST',
        data,
      })
    },
    // 22.1.5--全量地区树
    districtPlus(data) {
      //[code1,code2,code3]
      // return request({
      //   url: `${websiteUrl}/ent/district_plus`,
      //   method: 'POST',
      //   data,
      // })
      return new Promise((res, rej) => {
        wx.request({
          url: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/district_plus.json',
          headers: {
            'Content-Type': 'application/json',
          },
          success: (r) => {
            if (r.statusCode === 200) {
              res(r.data.data)
            }
          },
        })
      })
    },
    // 支付--订单
    order(data) {
      return request({
        url: `${websiteUrl}/payment/create/order`,
        method: 'POST',
        data,
      })
    },
    // 支付--订单
    orders(data) {
      return request({
        url: `${websiteUrl}/payment/create/service/order`,
        method: 'POST',
        data,
      })
    },
    newOrder(data) {
      return request({
        url: `${OderUrl}/payment/wechat_mini/create/order`,
        method: 'POST',
        data,
      })
    },
    payment(data) {
      return request({
        url: `${OderUrl}/payment/sign`,
        method: 'POST',
        data,
      })
    },
    swiper(data) { //asd_promote_type 0-开屏广告，1-首页banner，2-招商攻略广告
      return request({
        url: `${enterpriseUrl}/promote/adsPromoteData/query`,
        method: 'POST',
        data
      })
    },
    // 轮播埋点 
    eventTrack(data) { //asd_promote_type event_id
      return request({
        url: `${enterpriseUrl}/promote/userRecord`,
        method: 'POST',
        data
      })
    },
    // 查看是否支付状态 
    payStatus(pay_order_id) {
      return request({
        url: `${OderUrl}/payment/queryOrderStatus/${pay_order_id}`,
        method: 'GET'
      })
    },

  },
  //项目管理相关接口

  //  我的相关接口
  mine: {
    getMyData() {
      // 获取我的主页
      return request({
        url: `${userUrl}/customer/userData`,
        method: 'GET',
      })
    },
    getPushInfoNum() {
      // 获取推送信息
      return request({
        url: `${chainUrl}/my`,
        method: 'GET',
      })
    },
    handleInvoice(type = 'getlist', params) {
      //发票抬头相关
      let data = null,
        id = null
      const Type = {
        getlist: 'GET', // 获取我的发票抬头列表
        add: 'POST', // 添加发票抬头
        edit: 'PUT', // 修改发票抬头
        get: 'GET', // 获取我的发票抬头
        delet: 'DELETE', // 删除发票抬头
      }
      if (type == 'add' || type == 'edit') data = params
      id = params
      return request({
        url: `${enterpriseUrl}/invoice${type == 'delet' || type == 'get' ? '/' + id : ''}`,
        method: Type[type],
        data: data,
      })
    },
    reqFeedBack(type, params) {
      //意见反馈
      let data = null,
        id = null
      const Type = {
        add: 'POST', // 添加意见反馈
        get: 'GET', //获取意见详情
        getlist: 'POST', // 分页获取意见反馈
      }
      if (type == 'add' || type == 'getlist') data = params
      id = params
      return request({
        url: `${enterpriseUrl}/feedback${type == 'get' ? '/' + id : ''}${type == 'getlist' ? '/page' : ''
          }`,
        method: Type[type],
        data: data,
        hideLoading: type == 'getlist' ? true : false,
      })
    },
    // 消息列表
    reqNotice(params) {
      //消息通知
      return request({
        url: `${smsUrl}/message/list/subscribe?${params}`,
        method: 'GET',
      })
    },
    // 消息已读
    readNotice(id) {
      return request({
        url: `${smsUrl}/message/read/${id}`,
        method: 'PUT',
      })
    },
    // vip相关
    // combo() {
    //   return request({
    //     url: `${userUrl}/service/type/list`,
    //     method: 'GET',
    //   })
    // },
    combobtm() {
      return request({
        url: `${websiteUrl}/expand/list`,
        method: 'GET',
      })
    },
    combotop() {
      return request({
        url: `${userUrl}/service/type/list`,
        method: 'GET',
      })
    },
    orderlist(data) {
      return request({
        url: `${OderUrl}/payment/mine`,
        method: 'POST',
        data,
      })
    },
    firstPop(orgId) {
      return request({
        url: `${userUrl}/organizations/bind/service/${orgId}/first`,
        method: 'GET',
      })
    },
    // 分享相關 -- vip 
    generate() {
      return request({
        url: `${enterpriseUrl}/shareLink/generate`,
        method: 'POST',
      })
    },
    accept(data) {
      return request({
        url: `${websiteUrl}/invitation/accept`,
        method: 'POST',
        data,
      })
    },
    receiveAward(serial_number) {
      return request({
        url: `${enterpriseUrl}/shareLink/accept/${serial_number}`,
        method: 'POST',
      })
    },
    acceptList(serial_number) {
      //邀请后的列表
      return request({
        url: `${enterpriseUrl}/shareLink/queryBySerialNumber/${serial_number}`,
        method: 'GET',
      })
    },
    collectList(data) {
      //收藏类型
      return request({
        url: `${websiteUrl}/my/collect`,
        method: 'POST',
        data,
      })
    },
    // 签到
    entranceSigin() {
      return request({
        url: `${websiteUrl}/general/activity`,
        method: 'GET',
      })
    },
    siginDay(activity_id) {
      return request({
        url: `${websiteUrl}/general/activity/mine/${activity_id}`,
        method: 'GET',
      })
    },
    clickSign(activity_id) {
      return request({
        url: `${websiteUrl}/general/activity/sign/${activity_id}`,
        method: 'GET',
      })
    },

    // 右上角分享  -- 登录后保存在本地
    generatePageLink() {
      return request({
        url: `${enterpriseUrl}/shareLink/generatePageLink`,
        method: 'POST',
      })
    },
    //其它人接受右上角分享(具体待考虑)  -- 别人分享过来,从本地去取
    acceptPageLink(serial_number) {
      return request({
        url: `${enterpriseUrl}/shareLink/accept/pageLink/${serial_number}`,
        method: 'POST',
      })
    },

    // 开发票
    ...invoiceInfo,
    // 支付
    ...payAPI
  },
  preset: {
    //招商预设
    merchants(data) {
      //重点招商行业
      return request({
        url: `${chainUrl}/search/trade`,
        method: 'POST',
        data,
      })
    },
    indusList(data) {
      //招商产业链
      return request({
        url: `${chainUrl}/merchants/industry/list`,
        method: 'GET',
      })
    },
    save(data) {
      //保存招商预设
      return request({
        url: `${websiteUrl}/preset`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    get() {
      //保存招商预设
      return request({
        url: `${chainUrl}/preset`,
        method: 'GET',
        hideLoading: true,
      })
    },
    delet(id) {
      //保存招商预设
      return request({
        url: `${websiteUrl}/preset/${id}`,
        method: 'DELETE',
        hideLoading: false,
      })
    },
  },
  // 首页
  home: {
    // 权威榜单
    getDefinitiveList(data) {
      return request({
        url: `${chainUrl}/list/newUsablePage`,
        method: 'POST',
        data
      })
    },
    // 权威榜单单条查询
    getDefinitiveTop(data) {
      return request({
        url: `${chainUrl}/list/newUsableList/page`,
        method: 'POST',
        data
      })
    },
    introduction(data) {
      //招商攻略
      return request({
        // url: `${websiteUrl}/introduction`,
        url: `${websiteUrl}/introduction/list`,
        method: 'POST',
        data,
      })
    },
    introductionDetail(id, isShow) {
      //情报详情
      return request({
        url: `${websiteUrl}/introduction/${id}`,
        method: 'GET',
        hideLoading: !isShow ? false : true,
      })
    },
    introductList(id) {
      //相关情报推送列表
      return request({
        url: `${websiteUrl}/introduction/push/${id}`,
        method: 'GET',
        hideLoading: true,
      })
    },
    introductshouc(id) {
      return request({
        url: `${websiteUrl}/introduction/collect/${id}`,
        method: 'PUT',
        hideLoading: true,
      })
    },
    introductzan(id) {
      return request({
        url: `${websiteUrl}/introduction/thumbs/${id}`,
        method: 'PUT',
        hideLoading: true,
      })
    },
    portrait(data) {
      //首页-企业搜索
      return request({
        url: `${chainUrl}/industry/ent/portrait/list`,
        method: 'POST',
        data,
      })
    },
    // 关注列表
    getInterestList(data) {
      return request({
        url: `${chainUrl}/stocking/list`,
        method: 'POST',
        data,
      })
    },
    // 获取风险变动数量
    getInterestListDynamic(data) {
      return request({
        url: `${chainUrl}/stocking/count/dynamicList`,
        method: 'POST',
        data,
      })
    },
    // 删除关注
    delInterest(data) {
      return request({
        url: `${chainUrl}/collect/common/removeEnterprise/batch`,
        method: 'POST',
        data,
      })
    },
    saveTemplate(data) {
      //新增搜索模板
      return request({
        url: `${enterpriseUrl}/searchTemplate/save/template`,
        method: 'POST',
        data,
      })
    },
    delTemplate(id) {
      //删除搜索模板
      return request({
        url: `${enterpriseUrl}/searchTemplate/del/template/${id}`,
        method: 'GET',
      })
    },
    searTemplate() {
      //查询搜索模板
      return request({
        url: `${enterpriseUrl}/searchTemplate/search/template`,
        method: 'GET',
      })
    },

    addHistory(data) {
      //新增搜索历史
      return request({
        url: `${chainUrl}/search/history`,
        method: 'POST',
        data,
      })
    },
    getHistory(type) {
      //查询最近搜索
      return request({
        url: `${chainUrl}/search/history?model_type=${type}`,
        method: 'GET',
      })
    },
    clearHistory(type) {
      //清空最近搜索
      return request({
        url: `${chainUrl}/search/history?model_type=${type}`,
        method: 'DELETE',
      })
    },
    addBevHis(data) {
      //新增浏览历史
      return request({
        url: `${chainUrl}/behavior/history`,
        method: 'POST',
        data,
      })
    },
    detBevHis(type) {
      //删除浏览历史
      return request({
        url: `${chainUrl}/behavior/history?model_type=${type}`,
        method: 'DELETE',
      })
    },
    getBevHis(type) {
      //获取浏览历史
      return request({
        url: `${chainUrl}/behavior/history?model_type=${type}`,
        method: 'GET',
      })
    },
    connection(data) {
      //找关系
      return request({
        url: `${enterpriseUrl}/ent/connection`,
        method: 'POST',
        data,
      })
    },
    shareholder(id) {
      //找关系，找人员
      return request({
        url: `${enterpriseUrl}/v0.1/ent/shareholder/relation/${id}`,
        method: 'GET',
      })
    },
    connectionHis() {
      //获取关系链历史记录
      return request({
        url: `${enterpriseUrl}/v0.1/ent/connection/history`,
        method: 'GET',
      })
    },
    connectionHisParams(id) {
      return request({
        url: `${enterpriseUrl}/v0.1/ent/connection/history/${id}`,
        method: 'GET',
      })
    },
    // 生成企业关系链图谱
    connectionAtlas(data) {
      return request({
        url: `${enterpriseUrl}/ent/connection/download`,
        method: 'POST',
        data,
      })
    },
    // 企业关系链图谱下载历史记录
    downloadAtlasHistory(data) {
      return request({
        url: `${enterpriseUrl}/ent/connection/download/history`,
        method: 'POST',
        data
      })
    },
    // 更新下载图谱信息
    updateDownloadAtlas(data) {
      return request({
        url: `${enterpriseUrl}/ent/connection/uploadPlates`,
        method: 'PUT',
        data
      })
    },
    // 重新导出失败任务
    recallDownload(history_id) {
      return request({
        url: `${enterpriseUrl}/ent/connection/recallDownload/${history_id}`,
        method: 'POSt'
      })
    },
    // 科企查询
    statistics() {
      //获取关系链历史记录
      return request({
        url: `${chainUrl}/search/technological/statistics`,
        method: 'GET',
      })
    },
    //园区查询
    parkList(data) {
      return request({
        url: `${monitorUrl}/findPark/queryList`,
        method: 'POST',
        data,
      })
    },
    //园区内企业查询
    parkEntList(data) {
      return request({
        url: `${monitorUrl}/findPark/getParkEnterprise`,
        method: 'POST',
        data,
      })
    },
    financingList(data) {
      //融资事件查询-列表
      return request({
        url: `${chainUrl}/search/financing/events`,
        method: 'POST',
        data,
      })
    },
    financingHis(data) {
      //新增搜索历史(关键词)
      return request({
        url: `${websiteUrl}/financing/search/history`,
        method: 'POST',
        data,
      })
    },
    addFinancingHis(data) {
      //新增---新增搜索历史
      return request({
        url: `${websiteUrl}/financing/search/history`,
        method: 'POST',
        data,
      })
    },
    // delFinancingHis(data) {
    //   //删除---新增搜索历史
    //   return request({
    //     url: `${websiteUrl}/financing/search/history`,
    //     method: 'DEL',
    //     data,
    //   })
    // },
    delFinancingHis() {
      //删除---新增搜索历史
      return request({
        url: `${websiteUrl}/financing/search/history`,
        method: 'DELETE',
      })
    },
    addFLLHis(data) {
      //新增融资事件浏览历史
      return request({
        url: `${websiteUrl}/behavior/history`,
        method: 'POST',
        data,
      })
    },
    addFLLHisList(data) {
      //融资事件浏览历史--列表
      return request({
        url: `${websiteUrl}/financing/behavior/history`,
        method: 'GET',
        data,
      })
    },
    delFLLHisList() {
      //清空融资事件浏览历史
      return request({
        url: `${websiteUrl}/financing/behavior/history`,
        method: 'DELETE',
      })
    },
    financingGetHis() {
      //查询3个关键词
      return request({
        url: `${websiteUrl}/financing/search/history`,
        method: 'GET',
      })
    },
    financingRecommend() {
      //推荐词-3个
      return request({
        url: `${chainUrl}/search/financing/recommend`,
        method: 'GET',
      })
    },
    financingDetail(data) {
      //融资事件--详情页面
      return request({
        url: `${chainUrl}/search/financing/events/detail`,
        method: 'POST',
        data,
      })
    },
    getTabs() {
      // 获取首页tab
      return request({
        url: `${websiteUrl}/ent/first/page/tab`,
        method: 'get',
      })
    },
    getExpandList(data) {
      // 获取疑似扩招列表
      return request({
        url: `${websiteUrl}/ent/expansion`,
        method: 'POST',
        data,
      })
    },
    getBranchList(data) {
      // 获取新增分支机构列表
      return request({
        url: `${websiteUrl}/ent/branch`,
        method: 'POST',
        data,
      })
    },
    getRecommendEntList(data) {
      // 获取推荐企业列表
      return request({
        url: `${websiteUrl}/ent/recommend/list`,
        method: 'POST',
        data,
      })
    },
    // 22.10.31
    industrial(data) {
      //近期融资
      return request({
        url: `${websiteUrl}/ent/recently/financing`,
        method: 'POST',
        data,
      })
    },
    getIndustry(data) {
      //产业质讯
      return request({
        url: `${enterpriseUrl}/industry/news/query`,
        method: 'POST',
        data,
      })
    },
    hqdIntroductDetail(id, isShow) {
      //产业质讯详情
      return request({
        url: `${enterpriseUrl}/industry/news/${id}`,
        method: 'GET',
        hideLoading: !isShow ? false : true,
      })
    },
    hqdhotNews() {
      //产业质讯详情-热门推荐
      return request({
        url: `${enterpriseUrl}/industry/news/new`,
        method: 'POST',
      })
    },
    // 首页全部应用接口 
    applyList() {
      return request({
        url: `${enterpriseUrl}/apply/list?sys_code=small`,
        method: 'GET',
      })
    },
    applyAdd(data) { //调整首页服务
      return request({
        url: `${enterpriseUrl}/apply/add`,
        method: 'POST',
        data
      })
    },
    applyhisList() { //获取最近使用的应用
      return request({
        url: `${enterpriseUrl}/apply/history/list`,
        method: 'GET',
      })
    },
    applyhisAddList(data) { //新增最近使用的应用 
      return request({
        url: `${enterpriseUrl}/apply/history/add`,
        method: 'POST',
        data
      })
    },
  },


  // 商机-这个目前没用
  businessa: {
    business(data, type = 'POST') {
      //-新建商机
      return request({
        url: `${websiteUrl}/business`,
        method: type,
        data,
        hideLoading: false,
      })
    },
    eidtBusiness(data) {
      //-已购商机编辑
      return request({
        url: `${websiteUrl}/business/purchase/${data.id}`,
        method: 'PUT',
        data,
      })
    },
    getBazaarList(data) {
      //市场商机列表
      return request({
        url: `${websiteUrl}/business/web`,
        method: 'POST',
        data,
      })
    },
    homeList(data) {
      //市场商机列表
      return request({
        url: `${websiteUrl}/business/newest`,
        method: 'POST',
        data,
      })
    },
    detail(id) {
      //详情
      return request({
        url: `${websiteUrl}/business/${id}`,
        method: 'GET',
      })
    },
    query_detail(id) {
      //编辑后的已购商机--详情
      return request({
        url: `${websiteUrl}/business/query_purchase/${id}`,
        method: 'GET',
      })
    },
    interest(opportunityId, flag) {
      //传id
      return request({
        url: `${websiteUrl}/business/focus/${opportunityId}?flag=${flag}`,
        method: 'GET',
      })
    },
    about(id) {
      //相关商机
      return request({
        url: `${websiteUrl}/business/related/${id}`,
        method: 'GET',
      })
    },
    purchase(id) {
      //获取商机
      return request({
        url: `${websiteUrl}/business/purchase/${id}`,
        method: 'GET',
      })
    },
    busRoll() {
      //商机卷
      return request({
        url: `${websiteUrl}/commodity/left`,
        method: 'GET',
      })
    },
  },
  // 客商
  pub: {
    merchantAdd(data) {
      //-添加客商
      return request({
        url: `${websiteUrl}/merchant`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    getList(data) {
      return request({
        url: `${websiteUrl}/merchant/list`,
        method: 'POST',
        data,
      })
    },
    delItem(id) {
      return request({
        url: `${websiteUrl}/merchant/del/${id}`,
        method: 'GET',
      })
    },
    addProjectRecord(data) {
      //添加跟进
      return request({
        url: `${enterpriseUrl}/project/track`, //0直接新增，1删除代办计划并新增
        method: 'POST',
        data,
      })
    },
    record(data) {
      //添加跟进
      return request({
        url: `${websiteUrl}/merchant/follow/record`, //0直接新增，1删除代办计划并新增
        method: 'POST',
        data,
      })
    },
    person(data) {
      //添加联系人
      return request({
        url: `${websiteUrl}/merchant/contact/person`,
        method: 'POST',
        data,
      })
    },
    editProjectPerson(data) {
      //添加项目管理联系人
      return request({
        url: `${enterpriseUrl}/contact/person`,
        method: 'PUT',
        data,
      })
    },
    addProjectPerson(data) {
      //添加项目管理联系人
      return request({
        url: `${enterpriseUrl}/contact/person`,
        method: 'POST',
        data,
      })
    },
    getPerson(merchantId) {
      //获取联系人列表
      return request({
        url: `${websiteUrl}/merchant/contact/person/${merchantId}`,
        method: 'GET',
      })
    },
    getPersonList(id) {
      let temp = `project_id eq ${id}`
      return request({
        url: `${enterpriseUrl}/contact/person/list?$filter=${encodeURI(temp)}`,
        method: 'GET',
      })
    },

    addProjectPlan(data) {
      //添加跟进
      return request({
        url: `${enterpriseUrl}/track/plan`,
        method: 'POST',
        data,
      })
    },
    addPlan(data) {
      //添加跟进
      return request({
        url: `${websiteUrl}/merchant/plan`,
        method: 'POST',
        data,
      })
    },
    getRecord(merchantId) {
      //获取跟进记录
      return request({
        url: `${websiteUrl}/merchant/follow/record/${merchantId}`,
        method: 'GET',
      })
    },
    getPlan(merchantId) {
      //获取待办计划
      return request({
        url: `${websiteUrl}/merchant/plan/${merchantId}`,
        method: 'GET',
      })
    },
    focusList() {
      //选择商机列表
      return request({
        url: `${websiteUrl}/business/focus/list`,
        method: 'GET',
      })
    },
    minFocus(merchant_id) {
      //我绑定的商机
      return request({
        url: `${websiteUrl}/merchant/opportunity/${merchant_id}`,
        method: 'GET',
      })
    },
    addFoucus(data) {
      //绑定商机
      return request({
        url: `${websiteUrl}/merchant/bind/opportunity`,
        method: 'POST',
        data,
      })
    },
    cancel(merchant_id, opportunity_id) {
      //取消绑定
      return request({
        url: `${websiteUrl}/merchant/cancel/${merchant_id}/${opportunity_id}`,
        method: 'PUT',
      })
    },
    deletPlan(id) {
      return request({
        url: `${websiteUrl}/merchant/plan/${id}`,
        method: 'DELETE',
      })
    },
    getMerchantDetail(merchant_id) {
      return request({
        url: `${websiteUrl}/merchant/${merchant_id}`,
        method: 'GET',
      })
    },
    deletPerson(id) {
      //删除客商联系人
      return request({
        url: `${websiteUrl}/merchant/contact/person/${id}`,
        method: 'DELETE',
      })
    },
    eidtPerson(data) {
      //修改客商联系人
      return request({
        url: `${websiteUrl}/merchant/contact/person`,
        method: 'PUT',
        data,
      })
    },
  },
  // 筛选相关的接口
  filtrate: {},
  // 我的名片相关接口
  mycard: {
    // 园区/载体解绑
    unbindCard(data) {
      return request({
        url: `${enterpriseUrl}/card/unConnect`,
        method: 'POST',
        data,
      })
    },
    // 园区/载体绑定
    bindCard(data) {
      return request({
        url: `${enterpriseUrl}/card/connect`,
        method: 'POST',
        data,
      })
    },
    // 添加园区浏览记录
    addParkRecord(data) {
      return request({
        url: `${enterpriseUrl}/park/card/browse`,
        method: 'POST',
        data,
      })
    },
    // 获取园区名片列表(分页)
    getParkCardList(data) {
      return request({
        url: `${enterpriseUrl}/park/card`,
        method: 'POST',
        data,
      })
    },
    // 获取园区名片列表(不分页)
    getParkAllCardList() {
      return request({
        url: `${enterpriseUrl}/park/card`,
        method: 'GET',
      })
    },
    // 添加园区名片
    addParkCard(data) {
      return request({
        url: `${enterpriseUrl}/park/card/add`,
        method: 'POST',
        data,
      })
    },
    // 删除园区名片
    delParkCard(id) {
      return request({
        url: `${enterpriseUrl}/park/card/del/${id}`,
        method: 'GET',
      })
    },
    // 编辑园区名片
    editParkCard(data, id) {
      return request({
        url: `${enterpriseUrl}/park/card/${id}`,
        method: 'PUT',
        data,
      })
    },
    // 获取园区名片详情
    getParkDetail(id) {
      return request({
        url: `${enterpriseUrl}/park/card/${id}`,
        method: 'GET',
      })
    },
    // 添加载体名片
    addCarrierCard(data) {
      return request({
        url: `${enterpriseUrl}/card/add`,
        method: 'POST',
        data,
      })
    },
    // 删除载体名片
    delCarrierCard(id) {
      return request({
        url: `${enterpriseUrl}/card/del/${id}`,
        method: 'GET',
      })
    },
    // 编辑载体名片
    editCarrierCard(data, id) {
      return request({
        url: `${enterpriseUrl}/card/${id}`,
        method: 'PUT',
        data,
      })
    },
    // 获取载体名片详情
    getCarrierDetail(id) {
      return request({
        url: `${enterpriseUrl}/card/${id}`,
        method: 'GET',
      })
    },
    // 获取载体名片列表(要分页)
    getCarrierCardList(data) {
      return request({
        url: `${enterpriseUrl}/card`,
        method: 'POST',
        data,
      })
    },
    // 获取过滤掉已经绑定过园区的载体列表(不分页)
    getCarrierAllCardList(id) {
      return request({
        url: `${enterpriseUrl}/card/can_list/${id}`,
        method: 'GET',
      })
    },
    // 获取我的名片
    getMyCard() {
      return request({
        url: `${enterpriseUrl}/person/card`,
        method: 'GET',
      })
    },
    // 新增我的名片
    addMyCard(data) {
      return request({
        url: `${enterpriseUrl}/person/card`,
        method: 'POST',
        data,
      })
    },
    // 删除我的名片--测试
    delMyCard(id) {
      return request({
        url: `${enterpriseUrl}/person/card/${id}`,
        method: 'DELETE',
      })
    },
    // 修改我的名片
    updataMyCard(data) {
      return request({
        url: `${enterpriseUrl}/person/card/${data.id}`,
        method: 'PUT',
        data,
      })
    },
    // 新增收藏
    addCollectCard(data) {
      return request({
        url: `${enterpriseUrl}/person/card/scan`,
        method: 'POST',
        data,
      })
    },
    // 我收藏的名片
    myCollectCard(id) {
      return request({
        url: `${enterpriseUrl}/person/card/collect`,
        method: 'GET',
      })
    },
    // 收藏指定名片
    collectCard(id) {
      return request({
        url: `${enterpriseUrl}/person/card/collect/${id}`,
        method: 'PUT',
      })
    },
    // 查询指定名片
    getShareCard(id) {
      return request({
        url: `${enterpriseUrl}/person/card/target/${id}`,
        method: 'GET',
      })
    },
    //刪除名片
    delCard(id) {
      return request({
        url: `${enterpriseUrl}/card/del/${id}`,
        method: 'GET',
      })
    },
    // 取消收藏名片
    cancelCollect(id) {
      return request({
        url: `${enterpriseUrl}/person/card/cancel/${id}`,
        method: 'PUT',
      })
    },
  },
  // 企业相关接口
  company: {
    // 企业详情-工商信息
    getBusinessInfo(data) {
      return request({
        url: `${websiteUrl}/ent/business/info/${data}`,
        method: 'GET',
      })
    },
    // 企业列表
    getCompanyList(data) {
      return request({
        url: `${websiteUrl}/ent/list`,
        method: 'POST',
        data,
      })
    },
    // 企业详情
    getCompanyDetail(entId) {
      return request({
        url: `${websiteUrl}/ent/${entId}`,
        method: 'GET',
        hideLoading: false,
      })
    },
    // 企业详情联系方式
    getContact(id, data) {
      return request({
        url: `${chainUrl}/industry_admin/ent/contact/${id}?${serializeObj(data)}`,
        method: 'GET',
      })
    },
    // 企业详情-年检年报
    getYearlyInspect(entId, data) {
      return request({
        url: `${websiteUrl}/ent/annual/report/${entId}?${serializeObj(data)}`,
        method: 'GET',
        hideLoading: false,
      })
    },
    // 企业详情-专利信息
    getPatentList(data) {
      return request({
        url: `${websiteUrl}/ent/patent`,
        data: data,
        method: 'POST',
        hideLoading: false,
      })
    },
    // 企业详情-专利信息-专利详情
    getPatentDetail(entId) {
      return request({
        url: `${websiteUrl}/ent/patent/detail/${entId}`,
        method: 'GET',
        hideLoading: false,
      })
    },
    // 企业详情-对外投资
    getInvestmentList(data) {
      return request({
        url: `${websiteUrl}/ent/investment`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    // 疑似供应关系
    getSupplyList(data) {
      return request({
        url: `${websiteUrl}/ent/supply`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    // 企业详情-与本地企业关系-本地企业列表
    getLocalCompanyList(data) {
      return request({
        url: `${websiteUrl}/ent/local`,
        method: 'POST',
        data,
        hideLoading: false,
      })
    },
    // 企业详情-外链关系
    getOutRelationList(entId) {
      return request({
        url: `${websiteUrl}/ent/outreach/${entId}`,
        method: 'GET',
        hideLoading: false,
      })
    },
    action(entId) {
      return request({
        url: `${websiteUrl}/ent/judgement/action/${entId}`,
        method: 'GET',
      })
    },
    // 企业详情-客商推荐-产业链top前100企业列表
    // getRecommendCompanyList(data) {
    //   return request({url: `${websiteUrl}/local/ent/recommend`, method:'POST', data, hideLoading: false})
    // },
  },
  daily: {
    //监控日报相关
    monitor(data) {
      return request({
        url: `${newMonitorUrl}/monitor/monitorEntsListDetail`,
        method: 'POST',
        data,
      })
    },
    daily(data) {
      return request({
        url: `${newMonitorUrl}/monitor/newMonitorDailyReportsDetail`,
        method: 'POST',
        data,
      })
    },
    // 监控分组列表
    group(data) {
      return request({
        url: `${monitorUrl}/group/list`,
        method: 'POST',
        data,
      })
    },
    detail(data) {
      return request({
        url: `${newMonitorUrl}/monitor/newMonitorDailyReportDetailDetail`,
        method: 'POST',
        data,
      })
    },
    // 监控详情头部
    headInfo(entid) {
      return request({
        url: `${chainUrl}/search/ent/detail/${entid}`,
        method: 'GET',
      })
    },
    // 风险动态列表
    fxdtList(data) {
      return request({
        url: `${newMonitorUrl}/monitor/dynamicListDetail`,
        method: 'POST',
        data,
      })
    },
    // 舆情动态
    yqdtList(data) {
      return request({
        url: `${newMonitorUrl}/monitor/newsDynamicDetail`,
        method: 'POST',
        data,
      })
    },
    // 新增监控数量
    monitorEntCount() {
      return request({
        url: `${monitorUrl}/enter/monitorEntCount`,
        method: 'GET',
      })
    },
  },
  active: {
    //活动相关
    luckyList() {
      //转盘列表
      return request({
        url: `${websiteUrl}/general/activity`,
        method: 'GET',
      })
    },
    getlucky(activity_id) {
      //转盘奖励
      return request({
        url: `${websiteUrl}/general/activity/sign/${activity_id}`,
        method: 'GET',
      })
    },
    luckynum(activity_id) {
      //剩余次数
      return request({
        url: `${websiteUrl}/general/activity/left/${activity_id}`,
        method: 'GET',
      })
    },
    luckynumUp(activity_id) {
      //剩余次数
      return request({
        url: `${websiteUrl}/general/activity/share/callback/${activity_id}`,
        method: 'PUT',
      })
    },
  },
  project: {
    // 查询企业通用信息 
    getEntInfo(entId) {
      return request({
        url: `${chainUrl}/search/ent/detail/${entId}`,
        method: 'GET',
      })
    },
    deleteContact(data) {
      //添加跟进
      return request({
        url: `${enterpriseUrl}/contact/person`,
        method: 'DELETE',
        data,
      })
    },
    deleteProjectPlan(data) {
      //添加跟进
      return request({
        url: `${enterpriseUrl}/track/plan`,
        method: 'DELETE',
        data,
      })
    },
    changeProjectPlan(data) {
      //添加跟进
      return request({
        url: `${enterpriseUrl}/track/plan`,
        method: 'PUT',
        data,
      })
    },
    getBusinessRef(viewType) { // 权威榜单-榜单类型字典数据
      return request({
        url: `${enterpriseUrl}/project/management/board/${viewType}`,
        method: 'GET',
      })
    },
    getBusinessList(params) { // 权威榜单-榜单类型字典数据
      return request({
        url: `${enterpriseUrl}/project/management/list?${params}`,
        method: 'GET',
      })
    },
    deletProject(obj) { // 权威榜单-榜单类型字典数据
      return request({
        url: `${enterpriseUrl}/project/management`,
        method: 'DELETE',
        data: obj
      })
    },
    // 获取项目详情
    getProjectDetail(id) {
      return request({
        url: `${enterpriseUrl}/project/management/${id}`,
        method: 'get',
      })
    },
    //获取项目跟进信息
    getProjectTrack(id) {
      let temp = `project_id eq ${id}`
      return request({
        url: `${enterpriseUrl}/track/plan/list?$filter=${encodeURI(temp)}`,
        method: 'get',
      })
    },
    //获取项目跟进计划信息
    getProjectTrackPlan(id) {
      let temp = `project_id eq ${id}`
      return request({
        url: `${enterpriseUrl}/project/track/list?$filter=${encodeURI(temp)}`,
        method: 'get',
      })
    },
    changeProjectManage(data) {
      return request({
        url: `${enterpriseUrl}/project/management`,
        method: 'PUT',
        data
      })
    },
    // 获取项目成员列表
    getProjectMemberList() {
      return request({
        url: `${enterpriseUrl}/project/management/groupUsers`,
        method: 'GET',
      });
    },
    // 完成项目分配 
    completeDistribute(data) {
      return request({
        url: `${enterpriseUrl}/project/management/assignByIds`,
        method: 'PUT',
        data
      })
    },
    // 删除项目 
    delProject(data) {
      return request({
        url: `${enterpriseUrl}/project/management`,
        method: 'DELETE',
        data
      })
    }
  },
  business: {
    // 查业务
    // 查询关键词
    search(data) {
      return request({
        url: `${chainUrl}/industry/chain/keyWordSearch`,
        method: 'POST',
        data,
      })
    },
    // 调取搜索历史
    searchHis() {
      return request({
        url: `${chainUrl}/search/history`,
        method: 'GET',
      })
    },
    // 调取访问历史
    accessHis() {
      return request({
        url: `${chainUrl}/behavior/history`,
        method: 'GET',
      })
    },
    // 发起存储搜索历史记录
    storageHis(data) {
      return request({
        url: `${chainUrl}/search/history`,
        method: 'POST',
        data,
      })
    },
    // 发起存储访问历史记录
    storageAccessHis(data) {
      return request({
        url: `${chainUrl}/behavior/history`,
        method: 'POST',
        data,
      })
    },
    // 删除搜索历史
    deleteSearchDel() {
      return request({
        url: `${chainUrl}/search/history`,
        method: 'DELETE',
      })
    },
    // 删除浏览历史
    deleteBehavior() {
      return request({
        url: `${chainUrl}/behavior/history`,
        method: 'DELETE',
      })
    },
    // 查询产业链相关企业页面
    queryIndustryChain(data) {
      return request({
        url: `${chainUrl}/industry/chain/bigDataScreenCompanies`,
        method: 'POST',
        data,
      })
    },
    // 查询产业链
    queryIndustryChainList(data) {
      return request({
        url: `${chainUrl}/industry/chain/searchOtherRelate`,
        method: 'POST',
        data,
      })
    },
  },
  // 22.11.14
  smart: {
    infoSiper(data) {
      // 产业资讯轮播图
      return request({
        url: `${dshujuRrl}/industry/news/web`,
        method: 'POST',
        data,
      })
    },
    infoHot() {
      // 产业资讯热门推荐
      return request({
        url: `${dshujuRrl}/industry/news/tag/hot`,
        method: 'GET',
      })
    },
    infoTagList(id, data) {
      // 产业资讯热门推荐
      return request({
        url: `${dshujuRrl}/industry/news/tag/page/${id}`,
        method: 'POST',
        data,
      })
    },
    hyrzList(data) {
      // 行业融资列表
      return request({
        url: `${chainUrl}/financing/event/financingEventList`,
        method: 'POST',
        data,
      })
    },
    hyrzPop(data) {
      // 行业融资弹窗-当前融资轮次
      return request({
        url: `${chainUrl}/financing/event/dynamicFilterItems`,
        method: 'POST',
        data,
      })
    },
    rzxqHeadDetail(id) {
      // 融资详情基本信息
      return request({
        url: `${chainUrl}/financing/event/financingDetail/${id}`,
        method: 'GET',
      })
    },
    rzxqHisDetail(id) {
      // 融资详情融资历程
      return request({
        url: `${chainUrl}/financing/event/financingHistory/${id}`,
        method: 'GET',
      })
    },
    rzxqHotList(data) {
      // 融资详情相关推荐
      return request({
        url: `${chainUrl}/financing/event/relatedInformation`,
        method: 'POST',
        data,
      })
    },
    shangsiList(data) {
      // 上市企业列表接口
      return request({
        url: `${chainUrl}/industry/ent/portrait/list`,
        method: 'POST',
        data,
      })
    },
    getDensityStatistics(data) {
      // 基础设置-重点城市公用充电桩密度
      return request({
        url: `${dshujuRrl}/base/equipment/enterprises/statistics`,
        method: 'POST',
        data,
      })
    },
    getEnterprisesStatistics(data) {
      // 基础设置-各企业运营充电桩比例
      return request({
        url: `${dshujuRrl}/base/equipment/density/statistics`,
        method: 'POST',
        data,
      })
    },
    getEquipmentNumberStatistics(year) {
      // 基础设置-全国及重庆充电桩概况 接电站及加氢站概况
      return request({
        url: `${dshujuRrl}/base/equipment/number/statistics/min/${year}`,
        method: 'POST',
      })
    },
    getCarInventory(year) {
      // 查询各个年份的汽车保有量
      return request({
        url: `${dshujuRrl}/car/inventory/${year}`,
        method: 'GET',
      })
    },
    qiyeHot(data) {
      // 基础设置-全国及重庆充电桩概况 接电站及加氢站概况
      return request({
        url: `${chainUrl}/industry/ent/industryCode`,
        method: 'POST',
        data,
      })
    },
    getAtlas(obj) {
      // 产业图谱
      return request({
        url: `${chainUrl}/industry/chain/code/${obj.code}/plus/tag?area_code=${obj.area_code}`,
        method: 'GET',
      })
    },
    hrecommmend(data) {
      // 首页推荐
      return request({
        url: `${dshujuRrl}/ent/recommend`,
        method: 'POST',
        data,
      })
    },
    hcollectlist(data) {
      //推荐数组
      return request({
        url: `${chainUrl}/collect/common/collect/list`,
        method: 'POST',
        data,
      })
    },
  },
  // 产业资源相关接口
  indResource: {
    // 获取创新载体列表
    getCarrierList(data) {
      return request({
        url: `${dshujuRrl}/resource/bearings`,
        method: 'POST',
        data,
      })
    },
    // 获取创新载体详情
    getCarrierDetail(id) {
      return request({
        url: `${dshujuRrl}/resource/bearings/recommend/${id}`,
        method: 'GET',
      })
    },
    // 获取行业专家列表
    getExpertList(data) {
      return request({
        url: `${dshujuRrl}/resource/expert`,
        method: 'POST',
        data,
      })
    },
    // 获取行业专家详情
    getExpertDetail(id) {
      return request({
        url: `${dshujuRrl}/resource/expert/recommend/${id}`,
        method: 'GET',
      })
    },
    // 获取创新转化列表
    getTransformList(data) {
      return request({
        url: `${dshujuRrl}/resource/innovate`,
        method: 'POST',
        data,
      })
    },
    // 获取创新转化详情
    getTransformDetail(id) {
      return request({
        url: `${dshujuRrl}/resource/innovate/recommend/${id}`,
        method: 'GET',
      })
    },
    // 获取载体类型
    getCarrierType() {
      return request({
        url: `${dshujuRrl}/resource/bearingsType/all`,
        method: 'GET',
      })
    },
    // 获取载体属地
    getCarrierArea() {
      return request({
        url: `${dshujuRrl}/resource/bearings/area`,
        method: 'GET',
      })
    },
    // 获取平台属地
    getPlatformArea() {
      return request({
        url: `${dshujuRrl}/resource/innovate/area`,
        method: 'GET',
      })
    },
  },
  carrier: {
    getInstry(data) {
      return request({
        url: `${dshujuRrl}/industrySupporter/front/page`,
        method: 'POST',
        data,
      })
    },
    getLoadArea() {
      return request({
        url: `${dshujuRrl}/industrySupporter/load_area_tree`,
        method: 'GET',
      })
    },
    getParkTypes() {
      return request({
        url: `${dshujuRrl}/industrySupporter/list/type`,
        method: 'GET',
      })
    },
    getPageInfo(id) {
      return request({
        url: `${dshujuRrl}/industrySupporter/${id}`,
        method: 'GET',
      })
    },
    getHotParkList() {
      return request({
        url: `${dshujuRrl}/industrySupporter/recommend?$offset=0&$limit=5`,
        method: 'GET',
      })
    },
  },
  // 产业评测API
  industrialEvaluation: {
    ...industrialEvaluationAPI,
  },
  //产业对比
  compare: {
    economicComparison(data) {
      return request({
        url: `${dshujuRrl}/search/hdb/comparisonRegionalIndustries/statistic`,
        method: 'POST',
        data,
      })
    },
    industrialComparison(data) {
      return request({
        url: `${dshujuRrl}/search/hdb/industryChainComparison/statistic`,
        method: 'POST',
        data,
      })
    },
    link(data) {
      return request({
        url: `${dshujuRrl}/search/hdb/linkDistribution/statistic`,
        method: 'POST',
        data,
      })
    },
  },
  // 存量招商
  investment: {
    getCompanyList(data) {
      return request({
        url: `${dshujuRrl}/bigDataPlatform/importantCompany/detail`,
        method: 'POST',
        data,
      })
    },
    getMap(data) {
      return request({
        url: `${chainUrl}/stocking/supply/chains/static/map`,
        method: 'POST',
        data,
      })
    },
    getPie(data) {
      return request({
        url: `${chainUrl}/stocking/supply/chains/static`,
        method: 'POST',
        data,
      })
    },
    getCompanyListFirst(data) {
      return request({
        url: `${dshujuRrl}/bigDataPlatform/multiSupplyChainDetail`,
        method: 'POST',
        data,
      })
    },
    // 投资关系接口
    getPieTwo(data) {
      return request({
        url: `${dshujuRrl}/bigDataPlatform/modifyUserData/detail`,
        method: 'POST',
        data,
      })
    },
    getCompanyListSecond(data) {
      return request({
        url: `${dshujuRrl}/bigDataPlatform/foreignInvestment/detail`,
        method: 'POST',
        data,
      })
    },
    getCompanyListSecondHistory(data) {
      return request({
        url: `${dshujuRrl}/bigDataPlatform/foreignInvestmentHistory/detail`,
        method: 'POST',
        data,
      })
    },
    getPieThree(data) {
      return request({
        url: `${dshujuRrl}/bigDataPlatform/entContributionDistribution/detail`,
        method: 'POST',
        data,
      })
    },
    getFilterTag(data) {
      return request({
        url: `${dshujuRrl}/bigDataPlatform/foreignInvestmentFilterBox/detail`,
        method: 'POST',
        data,
      })
    },
  },
  // 产业发展接口
  evolution: {
    // 获取汽车产量情况
    getYield() {
      return request({
        url: `${dshujuRrl}/development/automobile/yield`,
        method: 'GET',
      })
    },
    // 获取汽车产量情况月度数据
    getYieldMonth() {
      return request({
        url: `${dshujuRrl}/development/automobile/yield/month`,
        method: 'GET',
      })
    },
    // 重点城市汽车产量变化率
    getCityRate(type) {
      return request({
        url: `${dshujuRrl}/development/city/automobile/yield?type=${type}`,
        method: 'GET',
      })
    },
    // 商用车、乘用车产量及比例
    getCarUseRatio() {
      return request({
        url: `${dshujuRrl}/development/commercial/multiplication/yield`,
        method: 'GET',
      })
    },
    // 重点企业发展趋势
    getIndustryTard(type) {
      return request({
        url: `${dshujuRrl}/development/industry/trend?type=${type}`,
        method: 'GET',
      })
    },
    // 重点企业营收变化
    getIndustryRevenue(type) {
      return request({
        url: `${dshujuRrl}/development/ent/revenue?type=${type}`,
        method: 'GET',
      })
    },
    // 新能源汽车销量占比
    getDevelopSale() {
      return request({
        url: `${dshujuRrl}/development/sale`,
        method: 'GET',
      })
    },
    // 智能网联汽车销量占比
    getSnatchedSale() {
      return request({
        url: `${dshujuRrl}/development/sale`,
        method: 'GET',
      })
    },
    // 车企销量排行榜
    getCarRanking(type, year) {
      return request({
        url: `${dshujuRrl}/development/ent/sale/ranking?type=${type}&year=${year}`,
        method: 'GET',
      })
    },
    // 获取汽车出口情况
    getCarExport() {
      return request({
        url: `${dshujuRrl}/development/export`,
        method: 'GET',
      })
    },
    // 获取只能网联市场规模预测 / 新能源汽车市场规模预测
    getForecastSize() {
      return request({
        url: `${dshujuRrl}/development/market/size`,
        method: 'GET',
      })
    },
    // 获取关键系统发展状况
    getMainSystemState(main, sub) {
      return request({
        url: `${dshujuRrl}/development/system/detail?main_system=${main}&subsystem=${sub}`,
        method: 'GET',
      })
    },
    // 获取主要企业
    getMainEnterprise(main, sub) {
      return request({
        url: `${dshujuRrl}/development/system/ent/list?main_system=${main}&subsystem=${sub}`,
        method: 'GET',
      })
    },
    // 获取零部件数据
    getComponentsData() {
      return request({
        url: `${dshujuRrl}/development/key/components`,
        method: 'GET',
      })
    },
  },
  // 发现页面相关接口
  publish: {
    // 获取所有操作手册
    getAllManual() {
      return request({
        url: `${enterpriseUrl}/manualClass/getAllManualClassDataList`,
        method: 'GET',
      })
    },
    // 获取操作手册详情
    getManualInfo(id) {
      return request({
        url: `${enterpriseUrl}/manualController/getManualById/${id}`,
        method: 'GET',
      })
    },
    // 获取所有更新动态
    getAllManualData() {
      return request({
        url: `${enterpriseUrl}/trendsController/getAllManualData`,
        method: 'GET',
      })
    },
    // 根据动态id获取更新详情
    getIDForManualData(id) {
      return request({
        url: `${enterpriseUrl}/trendsController/searchTrendsDataById/${id}`,
        method: 'GET',
      })
    },

  },
  // 查老板 
  boss: {
    bossInfo(data) {
      // 产业资讯轮播图
      return request({
        url: `${enterpriseUrl}/ent/boss_info_statistics`,
        method: 'POST',
        data,
      })
    },
  }
}