// 日期选择组件
const date = new Date();
const years = [];
const months = [];
const days = [];

for (let i = 1990; i <= date.getFullYear(); i++) {
  years.push(i.toString())
}

for (let i = 1; i <= 12; i++) {
  months.push(i.toString().padStart(2, '0'))
}

for (let i = 1; i <= 31; i++) {
  days.push(i.toString().padStart(2, '0'))
}
Component({
  /**
   * 组件的属性列表
   */
  properties: {

    // 日期选择器显示/隐藏
    visible: {
      type: Boolean,
      value: false
    },

    // 日期类型
    dateType: {
      type: String,
      value: ''
    },

    // 日期回填数据
    _date: {
      type: String,
      value: '',
    },
    title: {
      type: String,
      value: ''
    },
    changePadding: {
      type: Boolean,
      value: false
    }
  },

  observers: {
    _date: function (newDate) {
      let dateArr = newDate.split('-');
      this.backfillDate(dateArr);
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    years, // 所有年份的数据
    months, // 所有月份数据
    days, // 所有天数据
    year: '', // 当前选中的年份
    month: '', // 当前选中的月份
    day: '', // 当前选中的天天天天天天天天
    value: [], // 时间选择器默认选中数据
    fakeData: [], // 缓存数据
  },

  lifetimes: {
    attached: function () {
      this.initDate();
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {

    // 初始化日期
    initDate() {
      let {
        years,
        months,
        days,
        year,
        month,
        day,
        value
      } = this.data;
      year = date.getFullYear().toString(), // 当前选中的年份
        month = (date.getMonth() + 1).toString().padStart(2, '0'), // 当前选中的月份
        day = date.getDate().toString().padStart(2, '0'), // 当前选中的天天天天天天天天
        value[0] = years.findIndex((item) => item === year);
      value[1] = months.findIndex((item) => item === month);
      value[2] = days.findIndex((item) => item === day);
      this.setDays(value);
    },

    // 设置回填日期
    backfillDate(dateArr) {

      // 如果存在回填数据
      if (dateArr.length === 3) {
        let {
          years,
          months,
          days,
          value
        } = this.data;
        value = [
          years.findIndex(year => year === dateArr[0]),
          months.findIndex(month => month === dateArr[1]),
          days.findIndex(day => day === dateArr[2]),
        ];
        this.setDays(value, dateArr[2]);
      } else {
        this.initDate();
      }
    },

    // 修改日期时触发
    bindChange({
      detail: {
        value
      }
    }) {
      this.setData({
        fakeData: value
      })
    },
    bindEndChange() {
      let {
        fakeData
      } = this.data;
      this.setDays(fakeData);
    },

    // 根据选中的月份，判断当前月的天数
    setDays(value, curDay) {
      const bigMonth = ['01', '03', '05', '07', '08', '10', '12']; // 大月
      let {
        years,
        months
      } = this.data;
      let days = [],
        num = 0;

      // 如果当前月是大月
      if (bigMonth.includes(months[value[1]])) num = 31;

      // 如果当前月是二月
      if (months[value[1]] === '02') num = 28;

      // 如果是除二月的小月
      if (!bigMonth.includes(months[value[1]]) && months[value[1]] !== '02') num = 30;

      for (let i = 1; i <= num; i++) {
        days.push(i.toString().padStart(2, '0'))
      }
      this.setData({
        days
      })

      // 从更新后的days重新寻找当前选中的天的下标
      if (value[2] < 0) value[2] = days.findIndex(day => day === curDay);
      setTimeout(() => {
        this.setData({
          year: years[value[0]],
          month: months[value[1]],
          day: days[value[2]],
          value
        });
      })

    },

    // 确定
    submit() {
      let {
        year,
        month,
        day,
        dateType
      } = this.data;
      let date = year + '-' + month + '-' + day;
      this.triggerEvent('setDate', {
        date,
        dateType
      })
    },

    // 取消
    cancel() {
      this.triggerEvent('setDate', {})
    }
  }
})