import constant from '../../../../utils/constant'
import {
    home
} from '../../../../service/api';
import {
    getHeight
} from '../../../../utils/height';
import {
    debounce, formatDate
} from '../../../../utils/formate';
const app = getApp();
Page({
    data: {
        // 搜索相关
        inputShowed: false, //是否聚焦
        ent_name: "", //搜索值
        ents_name: "",
        historyList: wx.getStorageSync(constant.HistorySearch) || [],
        browsingHistory: [], //浏览历史
        timer: null,
        // 弹窗相关 
        popType: '',
        showVisible: false, //是否显示弹窗 
        title: '',
        content: '',
        selectedPerson: {
          index: '',
          item:{},
        }, // 当前选中的董监高
        // 浏览历史高度 
        scrollHeight: 'auto',
        filtrateHeight: 'auto',
        statusBarHeight: 'auto',
        // 筛选 
        dropDownMenuTitle: ['全国', '全部行业', '更多筛选'],
        // 卡片 下拉加载更多 
        cardHeight: 'auto',
        // 请求相关
        bazaarParms: {
            page_index: 1, //偏移量
            page_size: 10, //每页多少条
        },
        bazaarlist: [], //获取列表
        bazaarIsFlag: true, //节流 true允许
        bazaarHasData: true, //  是否还有数据
        bazaarIsNull: false, // list长度是否为0
        bazaarIsTriggered: false, // 下拉刷新状态,

        // 筛选弹窗内容 
        listIdx: -1,
        popList: [],
        isShowPop: false,
        activeIdx: -1, //弹窗高亮索引
        active_ent: {
            ent_id: '',
            ent_name: ''
        }
    },
    onShow() {
        this.scrollH()
        this.setData({
            bazaarlist: [], bazaarParms: { page_index: 1, page_size: 10 }
        })
        this.initGetList()
    },
    onLoad(options) {
        let index = +options?.curIndex
        this.setData({ listIdx: index })
    },
    vipPop(val) {
      this.setData({
        vipVisible: val
      })
    },
    onClear() {
        // console.log('111')
        this.setData({
            ent_name: '',
            ents_name: "",
            loading: false,
            inputShowed: false
        }, () => this.inputQuest())
    },
    onInput: debounce(function ([...e]) {
        let keyword = e[0].detail.value;
        this.setData({
            ent_name: keyword,
        }, () => this.inputQuest());
    }, 4000),
    goBack() {
        this.unLocked()

        wx.navigateBack({
            delta: 1, // 回退前 delta(默认为1) 页面
        })
    },
    unLocked() {
        // console.log(1)
        wx.hideKeyboard();
        this.setData({
            loading: false,
            inputShowed: false,
            ents_name: "",
            ent_name: ''
        })
    },
    inputQuest() {
        //处理input的数据到请求参数里面--过滤
        const { ent_name, bazaarParms } = this.data;
        bazaarParms['ent_name'] = ent_name
        bazaarParms.page_index = 1;
        console.log(bazaarParms['ent_name'] + 'a')
        // 
        this.setData({
            bazaarParms,
            bazaarlist: []
        }, () => this.initGetList(() => { }));
        // 
    },
    // 关键词处理
    onHandlerKewWord(list, key) {
      const { ent_name } = this.data;
      return list.reduce((result, items) => {
        const _tagName = items[key].replace(new RegExp(`(${ent_name})`), "*=*$1*=*");
        const split = _tagName.split("*=*");
        const handler = split.map(word => {
          if (word === ent_name) return { text: word, type: 'HIGH' }
          return { text: word, type: 'DEFAULT' }
        });
        return [...result, { ...items, label: handler }];
      }, []);
    },
    onClose() {
        this.setData({
            showVisible: false
        })
    },
    //获取小米列表 --避免后面一个请求覆盖前面一个请求
    async initGetList(callback) {
      app.showLoading('加载中')
        // 
        const that = this;
        let {
            bazaarlist,
            bazaarHasData,
            bazaarParms
        } = that.data;
        that.setData({
            bazaarIsNull: false,
            bazaarIsFlag: false,
            bazaarHasData: true
        });
        await home.portrait(bazaarParms).then(res => {
            let { items } = res
            let ary = []
            if (items.length < bazaarParms.page_size) bazaarHasData = false;
            ary = items.map((item, index) => {
                let { logo, ent_id, ent_name, tags, legal_person, reg_capital, register_date, location, official_website, register_address, collect } = item;
                register_date = formatDate(register_date, 'yyyy-MM-dd')
                return { logo, ent_id, ent_name, tags, legal_person, reg_capital, register_date, location, official_website, register_address, collect, index };
            })
            const list = this.onHandlerKewWord(bazaarlist.concat(ary), "ent_name");
            that.setData({
                bazaarlist: list,
                bazaarHasData,
                bazaarIsFlag: true
            }, () => {
                if (!that.data.bazaarlist.length) that.setData({
                    bazaarIsNull: true,
                    bazaarHasData: true
                });
            })
            callback && callback()
        }).catch(err => {
            callback && callback()
            app.showToast('获取数据失败!请稍后再试')
            console.log(err)
        })
        wx.hideLoading()
    },
    // 下拉刷新
    bazaarRefresher() {
        const that = this;
        wx.showNavigationBarLoading()
        let { bazaarParms } = that.data
        let obj = { ...bazaarParms, page_index: 1, page_size: 10 }
        that.setData({
            bazaarParms: obj,
            bazaarlist: [],
            bazaarHasData: true,
        }, () => that.initGetList(() => {
            that.setData({
                bazaarIsTriggered: false
            })
            wx.hideNavigationBarLoading()
        }))
    },
    //加载更多
    bazaarloadMore() {
      let {bazaarParms, bazaarHasData, bazaarIsFlag} = this.data;
      if (!bazaarHasData) return;
      if (!bazaarIsFlag) return; //节流
      bazaarParms.page_index += 1;
      this.setData({
        bazaarParms
      }, () => this.initGetList());
    },
    // 筛选
    onFlitter(e) {
        let { dropDownMenuTitle, bazaarParms, bazaarlist } = this.data
        const obj = e.detail;
        // dropDownMenuTitle[1] = obj.tittle 这个名字看后面放不放开
        delete obj['tittle'];
        bazaarParms = {
            ...bazaarParms,
            page_index: 1,
            page_size: 10,
            ...obj
        }
        dropDownMenuTitle[0] = bazaarParms?.areas?.length > 0 ? obj.name1 :  '全国'
        dropDownMenuTitle[1] = bazaarParms?.trade_types?.length > 0 ? obj.name2 :  '全部行业'
        bazaarlist = []
        this.setData({
            bazaarParms,
            bazaarlist,
            dropDownMenuTitle
        }, () => {
            app.showLoading('加载中...')
            this.initGetList(() => wx.hideLoading())
        })
    },
    //----------
    // 动态获取页面高度 
    scrollH() { //动态获取高度
      var that = this;
      getHeight(that, ['.searchs', '.drop-menu'], (data) => {
        const { screeHeight,res } = data;
        let h1 = res[0]?.height
        // 处理search外剩余的高度 
        let filtrateHeight = screeHeight - h1
        // 浏览历史的滚动高度 
        that.setData({
          filtrateHeight: filtrateHeight,
          cardHeight: screeHeight - res[1]?.height - res[1]?.top
        })
      })
    },
    onsure(e) { // 拿到结果 
      let { ent_id, ent_name } = e.currentTarget.dataset.item;
      const { listIdx } = this.data
      let params = { ent_id, ent_name, type: 'e' }
      let pages = getCurrentPages();
      let prevPage = pages[pages.length - 2]; //当前页面
      //上一页面
      prevPage.setData({ //直接给上移页面赋值
        [`related_args[${listIdx}]`]: params
      });
      wx.navigateBack({
        delta: 1
      })
    },
    async getPerson(e) { //拿到人员
      let { ent_id, ent_name } = e.currentTarget.dataset.item
      const res = await home.shareholder(ent_id)
      if (res.length < 0) {
        app.showToast('未知错误,请稍后再试!', 'none', 1200)
        return
      }
      this.setData({ popList: res, active_ent: { ent_id, ent_name } }, () => {
        this.setData({ isShowPop: true })
      })
    },
    getItem(e) {
      var index, item;
      if (e && e.currentTarget.dataset && Object.keys(e.currentTarget.dataset).length > 0) {
        index = e.currentTarget.dataset.index
        item = e.currentTarget.dataset.item
        this.setData({
          activeIdx: index,
          selectedPerson: { index,item }
        })
      } else { //点击了取消
        this.setData({ isShowPop: false, selectedPerson: { index: 0,item: {} } })
      }
    },
    getItemOK() {
      const { active_ent, listIdx, selectedPerson } = this.data
      if(selectedPerson.item.pid) {
        var obj = {
          ...active_ent,
          type: 'p', 
          person: selectedPerson.item.name,
          person_id: selectedPerson.item.pid,
          name: selectedPerson.item.name
        }
        // 将结果传到外面去 
        let pages = getCurrentPages();
        let prevPage = pages[pages.length - 2]; //当前页面
        //上一页面
        prevPage.setData({ //直接给上移页面赋值
          [`related_args[${listIdx}]`]: obj
        });
      }
      wx.navigateBack({
        delta: 1
      })
    },
});