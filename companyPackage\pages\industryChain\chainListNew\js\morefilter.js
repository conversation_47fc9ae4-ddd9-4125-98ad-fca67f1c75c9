import {REG_TIME_ENUM, ent_size_ENUM, benefits_assess_ENUM} from '../js/dict';
module.exports = Behavior({
  data: {
    minCapital: '', // 最低资本
    maxCapital: '', // 最高资本
    viewMinCapital: '', // 最低资本(页面展示用: 根据点击取消时minCapital的状态进行数据更新)
    viewMaxCapital: '', // 最高资本(页面展示用: 根据点击取消时maxCapital的状态进行数据更新)
    viewStartTime: '', // 注册年限开始时间(页面展示用: 根据点击取消时regTime.startTime的状态进行数据更新)
    viewEndTime: '', // 注册年限结束时间(页面展示用: 根据点击取消时regTime.endTime的状态进行数据更新)
    regCapital: {
      id: 'regCapital',
      title: '注册资本',
      cur: [], //选中的数据
      list: [],
      showTitle: true,
      singleSlect: true,
      slot: 'regCapital' // 自定义配置筛选项 插槽名称
    },
    title: '开始时间',
    dateType: '', // 日期类型
    backfillDate: '', // 打开日期弹窗时的回填日期
    regTime: {
      id: 'regTime',
      title: '注册年限',
      cur: [], //选中的数据
      list: [],
      canInput: true, // 是否能够输入(是否存在输入框，如果存在需要添加一个列表备份属性prevList存储上一次的list数据)
      startTime: '',
      endTime: '',
      showTitle: true,
      singleSlect: true,
      slot: 'regTime' // 自定义配置筛选项 插槽名称
    },
    entScale: {
      id: 'entScale',
      title: '企业规模',
      needVip: true, // 需要vip才能使用
      cur: [], //选中的数据
      list: [],
      showTitle: true,
      singleSlect: true,
      slot: 'entScale' // 自定义配置筛选项 插槽名称
    },
    benefitAssess: {
      id: 'benefitAssess',
      title: '效益评估',
      needVip: true, // 需要vip才能使用
      cur: [], //选中的数据
      list: [],
      showTitle: true,
      singleSlect: true,
      slot: 'benefitAssess' // 自定义配置筛选项 插槽名称
    }
  },
  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      this.getFilterData();
    }
  },
  methods: {
    getFilterData() {
      this.setData({
        'regTime.list': REG_TIME_ENUM,
        'entScale.list': ent_size_ENUM,
        'benefitAssess.list': benefits_assess_ENUM
      });
    }
  }
});
