/**
 * ConfigurableForm 组件样式
 * 基于原有 hunt 组件样式，支持主题配置
 */

@import "./styles/variables.scss";
@import "./styles/mixins.scss";

/* 主容器样式 */
.searWrap {
  display: flex;
  background: $background-color;
  padding-top: 12rpx;

  &--simplified {

    // 简化版特殊样式
    .searWrap-r {
      width: 180rpx; // 稍微窄一些
    }

    .searWrap-l {
      width: 570rpx;
    }
  }

  &--full {
    // 完整版样式（默认）
  }
}

/* 左侧菜单样式 */
.searWrap-r {
  width: 208rpx;

  ::-webkit-scrollbar {
    display: inline-block;
    width: 2rpx !important;
    height: 2rpx !important;
    color: #ffffff;
  }

  ::-webkit-scrollbar-track {
    border-radius: 10rpx;
    background-color: #fff;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 10rpx;
    -webkit-box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.3);
    box-shadow: inset 0 0 6rpx rgba(0, 0, 0, 0.1);
  }
}

/* 右侧内容样式 */
.searWrap-l {
  width: 542rpx;

  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }
}

/* 左侧菜单项样式 */
.searL {
  width: 100%;
  height: 100%;

  .active {
    position: relative;
    background: #fff;
    border-radius: 16rpx 0 0 16rpx;

    &::after {
      @include menu-corner(top);
    }

    &::before {
      @include menu-corner(bottom);
    }

    .tit::after {
      @include active-indicator;
    }
  }

  .tit {
    position: relative;
    width: 100%;
    height: 100rpx;
    display: flex;
    align-items: center;
    padding-left: 32rpx;

    .tit-text {
      font-size: 28rpx;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: $text-color-primary;
    }

    .tit-icon {
      width: 20rpx;
      height: 20rpx;
      margin-left: 12rpx;
    }
  }

  .active .tit .tit-text {
    font-weight: 600;
  }

  .tit_cont {
    overflow: hidden;
    transition: all 0.3s;

    &--open {
      height: auto;
    }

    &--close {
      height: 0;
    }

    .child-item {
      height: 88rpx;
      padding-left: 32rpx;
      font-size: 24rpx;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: $text-color-secondary;
      line-height: 88rpx;
    }

    .child-active {
      position: relative;
      color: $primary-color;
      font-weight: 600;
      font-size: 24rpx;

      &::after {
        @include child-active-indicator;
      }
    }
  }
}

/* 内容区域样式 */
.content {
  width: 100%;
  background-color: #fff;
  padding: 0 32rpx 32rpx;
  overflow-y: auto;
  border-radius: 16rpx 16rpx 0px 0px;

  .content-item-pop {
    position: relative;
    display: flex;
    justify-content: space-between;
    height: 92rpx;
    align-items: center;

    &::after {
      @include border-bottom;
    }

    .title {
      font-size: 28rpx;
      font-family:
        PingFang SC-Semibold,
        PingFang SC;
      font-weight: 400;
      color: $text-color-primary;
      flex-shrink: 0;
    }

    .ipt {
      position: relative;
      flex: 1;
      margin-left: 28rpx;

      .ipt-input {
        font-size: 28rpx;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: $primary-color;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .content-item-pop-r {
      display: inline-flex;
      align-items: center;
      flex: 1;

      .pop-text {
        font-size: 28rpx;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: $text-color-placeholder;
        flex: 1;
        margin-left: 28rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        overflow: hidden;

        &.has {
          color: $primary-color;
        }
      }

      .pop-icon {
        width: 24rpx;
        height: 24rpx;
        margin-left: 10rpx;
      }
    }
  }

  .cnt-ipt {
    position: relative;
    margin-top: 40rpx;

    &::before {
      @include border-top;
    }
  }

  .content-item {
    font-size: 25rpx;
    color: $text-color-secondary;
    padding: 48rpx 0 0rpx;
    background-color: #fff;

    .tit {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: $text-color-primary;

      &--active {
        font-weight: 600;
      }
    }

    .tit-text {
      @include section-title;
    }

    .zhankai {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      font-size: 28rpx;

      &--active {
        font-weight: 600;
      }

      .zhankai-icon {
        width: 24rpx;
        height: 24rpx;
        padding-left: 30rpx;
      }
    }

    .wrap {
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;
      transition: all 1s ease;

      &--limited {
        height: 174rpx;
      }

      &--auto {
        height: auto;
      }

      .tag-item {
        @include tag-style;

        &.active {
          @include tag-active-style;
        }
      }

      .input-wrap {
        @include input-wrap-style;

        .input-item {
          @include input-item-style;

          &.active {
            @include input-active-style;
          }

          .year {
            flex-shrink: 0;
            line-height: 56rpx;
          }

          .input-field {
            height: 100%;
          }

          .short-line {
            width: 200rpx;
            height: 100%;
            line-height: 52rpx;
            text-align: center;
          }
        }
      }
    }
  }
}

/* 搜索建议样式 */
.child-box {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  max-height: 300rpx;

  .child-box-ul {
    max-height: 300rpx;
  }

  .search-li {
    padding: 20rpx 28rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .listtext {
      font-size: 28rpx;
      color: $text-color-primary;

      &.searchHigh {
        color: $primary-color;
        font-weight: 600;
      }
    }
  }

  .hover {
    background-color: #f8f8f8;
  }
}

/* 占位符样式 */
.placeCls {
  font-size: 28rpx;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: $text-color-placeholder;
}

/* VIP 标识 */
.vip {
  @include vip-badge;
}

/* 说明图标 */
.filterexp {
  @include info-icon;
}

/* 特殊背景样式 */
.yxbg {
  // 产业优选背景
  position: relative;
  width: 100% !important;
  margin-top: 48rpx;

  .tit-text {
    position: absolute;
    top: 16rpx;
    width: 100%;
    z-index: 10;
    height: 100%;
    font-size: 32rpx;
    font-weight: 600;
    color: #20263A;
  }

  .yxbgImg {
    position: absolute;
    left: -24rpx;
    right: -24rpx;
    top: 0;
    background: #F9F2E2;
    width: 526rpx;
    height: 120rpx;
    border-top-right-radius: 8rpx;
    border-top-left-radius: 8rpx;
    overflow: hidden;
  }
}

.yxbglt {
  // 龙头企业背景
  position: relative;
  z-index: 1;
  margin-top: 36rpx;
  width: 526rpx;
  transform: translateX(-24rpx);
  border-left: 8rpx solid #F9F2E2;
  border-right: 8rpx solid #F9F2E2;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  padding: 36rpx 16rpx 20rpx !important;
}

.yxbglt1 {
  width: 526rpx;
  transform: translateX(-24rpx);
  border-left: 8rpx solid #F9F2E2;
  border-right: 8rpx solid #F9F2E2;
  padding: 20rpx 16rpx 20rpx !important;
}

.yxbgend {
  // 科技型企业背景
  width: 526rpx;
  transform: translateX(-24rpx);
  border-left: 8rpx solid #F9F2E2;
  border-right: 8rpx solid #F9F2E2;
  border-bottom: 8rpx solid #F9F2E2;
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  padding: 20rpx 16rpx 20rpx !important;
}

.yxbgImg {
  .bg-image {
    width: 100%;
    height: auto;
  }
}