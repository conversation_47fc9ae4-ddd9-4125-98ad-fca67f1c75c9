// 面积单位
export const AREA_UNIT = {
  M2: '㎡',
  MU: '亩'
};

export const PROJECT_TYPE = {
  //园区性质
  GOVERNMENT_PARK: '政府园区',
  GOVERNMENT_PARTICIPATION: '政府参股',
  PRIVATE_PARK: '民营园区',
  COUNTRY_JOINT: '央企国企合资',
  COUNTRY_ONLY: '央企国企独资',
  // 招商类型
  PROJECT: '投资项目',
  LAND: '土地',
  WAREHOUSE: '厂房仓库',
  OB: '写字楼',
  OTHER: '其他'
};
export const tabs = [
  {
    key: 'INDUSTRY_NEWS',
    value: '产业快讯'
  },
  {
    key: 'IMPORT_PROCESS',
    value: '重点环节'
  },
  {
    key: 'NEW_TECHNOLOGY',
    value: '前沿技术'
  },
  {
    key: 'head_ent_flagERPRISES',
    value: '龙头企业'
  },
  {
    key: 'BUSINESS_NEWS',
    value: '招商资讯'
  }
];

export const industry_news_type_em = {
  INDUSTRY_NEWS: '产业快讯',
  IMPORT_PROCESS: '重点环节',
  NEW_TECHNOLOGY: '前沿技术',
  head_ent_flagERPRISES: '龙头企业',
  BUSINESS_NEWS: '招商资讯'
};
