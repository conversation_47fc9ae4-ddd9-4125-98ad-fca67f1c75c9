/**
 * 组件交互工具
 * 统一管理组件间的交互逻辑
 */

const {
  utils
} = require('./helpers');

/**
 * 组件操作工具
 */
const componentHelpers = {
  /**
   * 清空子组件
   * @param {Object} context - 组件上下文
   * @param {string} selector - 组件选择器
   * @returns {Function} 清空函数
   */
  clearChildComponent(context, selector = '#configurable-form') {
    let childComponent = context.selectComponent(selector);

    return function () {
      if (childComponent) {
        childComponent.clearSear?.();
      } else {
        childComponent = context.selectComponent(selector);
        childComponent?.clearSear?.();
      }
    };
  },

  /**
   * 回填子组件数据
   * @param {Object} context - 组件上下文
   * @param {string} selector - 组件选择器
   * @returns {Function} 回填函数
   */
  fillChildComponent(context, selector = '#configurable-form') {
    let childComponent = context.selectComponent(selector);

    return function (data) {
      if (childComponent) {
        childComponent.setBackfillData?.(data);
      } else {
        childComponent = context.selectComponent(selector);
        childComponent?.setBackfillData?.(data);
      }
    };
  },

  /**
   * 获取子组件实例
   * @param {Object} context - 组件上下文
   * @param {string} selector - 组件选择器
   * @returns {Object|null} 子组件实例
   */
  getChildComponent(context, selector) {
    return context.selectComponent(selector);
  },

  /**
   * 更新组件状态
   * @param {Object} context - 组件上下文
   * @param {Object} state - 状态对象
   * @param {Function} callback - 回调函数
   */
  updateComponentState(context, state, callback) {
    if (typeof state === 'object' && state !== null) {
      context.setData(state, callback);
    }
  },

  /**
   * 批量更新组件状态
   * @param {Object} context - 组件上下文
   * @param {Array} updates - 更新数组
   */
  batchUpdateState(context, updates) {
    const mergedState = {};

    updates.forEach(update => {
      if (typeof update === 'object' && update !== null) {
        Object.assign(mergedState, update);
      }
    });

    if (Object.keys(mergedState).length > 0) {
      context.setData(mergedState);
    }
  },

  /**
   * 组件状态缓存
   */
  _stateCache: new Map(),

  /**
   * 缓存组件状态
   * @param {string} key - 缓存键
   * @param {Object} state - 状态对象
   */
  cacheState(key, state) {
    this._stateCache.set(key, JSON.parse(JSON.stringify(state)));
  },

  /**
   * 获取缓存的状态
   * @param {string} key - 缓存键
   * @returns {Object|null} 缓存的状态
   */
  getCachedState(key) {
    const cached = this._stateCache.get(key);
    return cached ? JSON.parse(JSON.stringify(cached)) : null;
  },

  /**
   * 清除状态缓存
   * @param {string} key - 缓存键，不传则清除所有
   */
  clearStateCache(key) {
    if (key) {
      this._stateCache.delete(key);
    } else {
      this._stateCache.clear();
    }
  }
};

// 事件处理工具 节流防抖
const eventHelpers = {
  /**
   * 创建防抖事件处理器
   * @param {Function} handler - 事件处理函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 防抖后的处理器
   */
  debounceHandler(handler, delay = 300) {
    return utils.debounce(handler, delay);
  },

  /**
   * 创建节流事件处理器
   * @param {Function} handler - 事件处理函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 节流后的处理器
   */
  throttleHandler(handler, delay = 300) {
    return utils.throttle(handler, delay);
  },

  /**
   * 创建事件处理器
   * @param {Function} handler - 处理函数
   * @param {Object} options - 选项
   * @returns {Function} 事件处理器
   */
  createEventHandler(handler, options = {}) {
    const {
      debounce = false,
        throttle = false,
        delay = 300,
        preventDefault = false,
        stopPropagation = false
    } = options;

    let wrappedHandler = handler;

    // 应用防抖或节流
    if (debounce) {
      wrappedHandler = this.debounceHandler(handler, delay);
    } else if (throttle) {
      wrappedHandler = this.throttleHandler(handler, delay);
    }

    return function (event) {
      // 阻止默认行为
      if (preventDefault && event.preventDefault) {
        event.preventDefault();
      }

      // 阻止事件冒泡
      if (stopPropagation && event.stopPropagation) {
        event.stopPropagation();
      }

      return wrappedHandler.call(this, event);
    };
  },

  /**
   * 事件监听器管理
   */
  _listeners: new Map(),

  /**
   * 添加事件监听器
   * @param {string} eventName - 事件名称
   * @param {Function} handler - 处理函数
   * @param {string} id - 监听器ID
   */
  addEventListener(eventName, handler, id = utils.generateId()) {
    if (!this._listeners.has(eventName)) {
      this._listeners.set(eventName, new Map());
    }

    this._listeners.get(eventName).set(id, handler);
    return id;
  },

  /**
   * 移除事件监听器
   * @param {string} eventName - 事件名称
   * @param {string} id - 监听器ID
   */
  removeEventListener(eventName, id) {
    const eventListeners = this._listeners.get(eventName);
    if (eventListeners) {
      eventListeners.delete(id);

      if (eventListeners.size === 0) {
        this._listeners.delete(eventName);
      }
    }
  },

  /**
   * 触发事件
   * @param {string} eventName - 事件名称
   * @param {any} data - 事件数据
   */
  emit(eventName, data) {
    const eventListeners = this._listeners.get(eventName);
    if (eventListeners) {
      eventListeners.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${eventName}:`, error);
        }
      });
    }
  },

  /**
   * 清除所有事件监听器
   */
  clearAllListeners() {
    this._listeners.clear();
  }
};

// 数据绑定工具---暂时没用
const dataBindingHelpers = {
  /**
   * 创建双向数据绑定
   * @param {Object} context - 组件上下文
   * @param {string} dataPath - 数据路径
   * @returns {Object} 绑定对象
   */
  createTwoWayBinding(context, dataPath) {
    return {
      get value() {
        return this.getValue(dataPath);
      },

      set value(newValue) {
        this.setValue(dataPath, newValue);
      },

      getValue(path) {
        const keys = path.split('.');
        let current = context.data;

        for (const key of keys) {
          if (current && typeof current === 'object') {
            current = current[key];
          } else {
            return undefined;
          }
        }

        return current;
      },

      setValue(path, value) {
        const updateObj = {};
        updateObj[path] = value;
        context.setData(updateObj);
      },

      bind(inputElement) {
        if (inputElement && inputElement.addEventListener) {
          inputElement.addEventListener('input', e => {
            this.value = e.detail.value;
          });
        }
      }
    };
  },

  /**
   * 监听数据变化
   * @param {Object} context - 组件上下文
   * @param {string} dataPath - 数据路径
   * @param {Function} callback - 回调函数
   */
  watchData(context, dataPath, callback) {
    const originalSetData = context.setData;

    context.setData = function (data, cb) {
      const hasTargetPath = dataPath in data;

      originalSetData.call(this, data, () => {
        if (hasTargetPath && typeof callback === 'function') {
          callback(data[dataPath], dataPath);
        }
        if (typeof cb === 'function') {
          cb();
        }
      });
    };
  }
};


// 导出模块
module.exports = {
  componentHelpers: componentHelpers,
  eventHelpers: eventHelpers,
  dataBindingHelpers: dataBindingHelpers,
};