import {
  home,
} from '../../../../service/api'
import {
  getHeight
} from '../../../../utils/height';
import {
  hasPrivile
} from '../../../../utils/route'
import {
  preventActive
} from '../../../../utils/util';
import {
  collect
} from '../../../../utils/mixin/collect'
import {
  formatDate,
  handleSearchHight
} from '../../../../utils/formate';
import {
  getShareUrl,
  handleShareUrl
} from '../../../../utils/mixin/pageShare'
import {
  commonRequest
} from '../../../../utils/mixin/loginbeforeReq'
var checkMixin = require('../../../../utils/mixin/check')

const app = getApp();
Page({
  behaviors: [checkMixin()],
  onLoad(option) {
    handleShareUrl()
    if (!option?.obj) {
      // 登录后重新刷新这个页面没有参数
      return
    }
    let {
      type,
      name
    } = JSON.parse(decodeURIComponent(option.obj))
    this.setData({
      obj: option.obj
    })
    wx.setNavigationBarTitle({
      title: name,
    })
    this.setData({
      'bazaarParms.technology': [type],
      filterVal: type,
      type,
      name
    }) //显示数量，一个用于发请求 
    commonRequest()
  },
  data: {
    contact_id: '',
    recordY: 0,
    privileFlag: true,
    dropDownMenuTitle: ['所属地区', '行业类型', '更多筛选'],
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: 10, //每页多少条
      technology: []
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态
    count: 0
  },
  handleLogin() { //已经登录触发的函数
    // if (this.data.bazaarlist?.length > 0) {
    //   return
    // }
    app.showLoading()
    this.initGetList(() => {
      wx.hideLoading()
    })
  },
  handleLogout() {
    app.showLoading()
    this.initGetList(() => {
      wx.hideLoading()
    })
  },
  onShow() {},
  onReady() {
    this.getHeight()
  },
  //获取小米列表
  initGetList(callback) {
    const that = this;
    let {
      bazaarlist,
      bazaarHasData,
      bazaarParms
    } = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    if (bazaarParms.page_index > 5) {
      hasPrivile({
        privileType: '科技型企业-列表页'
      }).then(res => {
        this.setData({
          privileFlag: res
        })
      })
      this.setData({
        bazaarHasData: true,
        bazaarIsFlag: true,
      })
      return
    }
    home.portrait(bazaarParms).then(res => {
      let {
        items,
        count
      } = res
      let ary = []
      if (items.length < bazaarParms.page_size) bazaarHasData = false;
      ary = items.map((item, index) => {
        item.register_date = formatDate(item.register_date, 'yyyy-MM-dd')
        item.heightKey = bazaarParms['ent_name']
        return item
      })
      ary = handleSearchHight(ary, 'ent_name', that.data.ent_name)
      that.setData({
        bazaarlist: bazaarlist.concat(ary),
        bazaarHasData,
        bazaarIsFlag: true,
        count
      }, () => {
        if (!that.data.bazaarlist.length) that.setData({
          bazaarIsNull: true,
          bazaarHasData: true,
          count: 0
        });
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      that.setData({
        bazaarlist: [],
        bazaarIsNull: true,
        bazaarHasData: true,
        count: 0
      });
      console.log(err)
    })
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    wx.showNavigationBarLoading()
    let {
      bazaarParms,
      type
    } = that.data
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10,
      'technology': [type]
    }
    that.setData({
      bazaarParms: obj,
      bazaarlist: [],
      bazaarHasData: true,
    }, () => that.initGetList(() => {
      that.setData({
        bazaarIsTriggered: false
      })
      wx.hideNavigationBarLoading()
    }))
  },
  //加载更多
  bazaarloadMore() {
    let {
      bazaarParms,
      bazaarHasData,
      bazaarIsFlag
    } = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData({
      bazaarParms
    }, () => this.initGetList());
  },
  isScrollUp(e) {
    if (this.data.recordY > e.detail.scrollTop) {
      this.setData({
        privileFlag: true
      })
    }
    this.setData({
      recordY: e.detail.scrollTop
    })
  },
  // 筛选
  onFlitter(e) {
    let {
      dropDownMenuTitle,
      bazaarParms,
      bazaarlist,
      type
    } = this.data
    const obj = e.detail;
    dropDownMenuTitle[0] = obj.name1
    dropDownMenuTitle[1] = obj.name2,
      delete obj['name1'];
    delete obj['name2'];
    bazaarParms = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10,
      'technology': [type],
      ...obj
    }
    bazaarlist = []
    this.setData({
      bazaarParms: bazaarParms,
      bazaarlist,
      dropDownMenuTitle
    }, () => {
      // app.showLoading('加载中...')
      // this.initGetList(() => wx.hideLoading())
      this.bazaarRefresher()
    })
  },
  login() {
    const {
      obj
    } = this.data;
    const url = `/subPackage/pages/science/scienceDetail/index?obj=${encodeURIComponent(JSON.stringify(obj))}`;
    app.route(this, `/pages/login/login?url=${url}`);
  },
  async onCard(data) {
    let that = this
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type
      const comDetail = data.detail.data
      // console.log(index)
      // 处理收藏 
      if (type == 'collect') {
        collect(that, comDetail, 'bazaarlist')
      } else if (type == 'relation') {
        this.setData({
          contact_id: comDetail.ent_id,
          showContact: true
        })
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location.lat,
            lon: +comDetail.location.lon,
          },
          locationTxt: comDetail.register_address,
          addmarkers: [{
            id: 1,
            latitude: +comDetail.location.lat,
            longitude: +comDetail.location.lon,
            iconPath: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png",
            width: 20,
            height: 20,
          }],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location.lat, //维度
            longitude: +comDetail.location.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        })
      }
    })
  },
  goMap() {
    const {
      locationMap
    } = this.data
    wx.openLocation(locationMap)
  },
  onCloseContact() {
    this.setData({
      showContact: false
    })
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    })
  },
  makeCall(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
    // console.log(item.contact_data)
    wx.makePhoneCall({
      phoneNumber: item.contact_data,
    })
  },
  // 去详情页面 
  goDetail(e) {
    let {
      enterprise_id
    } = e.currentTarget.dataset.item
    // let url = `/subPackage/pages/companyDetail/companyDetail?id=${enterprise_id}`;
    // app.route(this, url)
    const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${enterprise_id}`)
    app.route(this, `/subPackage/pages/webs/index?url=${url}`)
  },
  getHeight() {
    const that = this;
    getHeight(that, ['.sci_line'], (data) => {
      let {
        res,
        screeHeight
      } = data
      let cardHeight = screeHeight - res[0]?.top - res[0]?.height
      that.setData({
        cardHeight
      })
    })
  },
  vipPop() {
    this.setData({
      vipVisible: true
    })
  },
  // obj=${encodeURIComponent(JSON.stringify(item))} count
  onShareAppMessage: function () {
    return {
      title: `邀请你查看${this.data.count}家${this.data.name}名单`, //自定义转发标题
      path: getShareUrl(`/subPackage/pages/science/scienceDetail/index?obj=${this.data.obj}`), //分享页面路径
      imageUrl: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/h3.png' //图片后面换
    };
  },
})