import {renderList, params} from '../../../../../../components/hunt/mixin.js';
var comMixin = require('../../../../../../components/hunt/common/common');
let searchDataList = renderList.filter(i => {
  if (
    [
      'reg_capital',
      'areas',
      'ent_size',
      'est_date',
      'trade_types',
      'benefits_assess'
    ].includes(i.type)
  )
    return i;
});
Component({
  behaviors: [comMixin],
  data: {
    itemList: JSON.parse(JSON.stringify(searchDataList)), // 页面静态数据
    leftList: JSON.parse(JSON.stringify(searchDataList)) // 页面静态数据-右边
  },
  methods: {
    clearSear() {
      // 清空模版
      // 后面copy组件的时候renderList名字会跟着换
      let list = searchDataList.map(item => {
        if (item.isOpenIcon) {
          item.isOpen = this.data.itemList.filter(
            i => item.type === i.type
          )[0]?.isOpen;
        }
        return item;
      });
      this.setData(
        {
          itemList: JSON.parse(JSON.stringify(list)),
          params: JSON.parse(JSON.stringify(params)),
          minCapital: '',
          maxCapital: '',
          minDate: '',
          maxDate: '',
          date: '',
          capitalActive: false,
          socialActive: false,
          socialminPeson: '',
          socialmaxPeson: '',
          dateActive: false,
          dateType: ''
        },
        () => {
          // console.log(this.data.params)
        }
      );
    }
  },
  pageLifetimes: {
    show() {
      let {wrapHeight, isIphoneX} = this.data;
      // 计算容器高度
      if (!wrapHeight) {
        this.setData({
          wrapHeight: `calc(100vh - ${isIphoneX ? '168rpx' : '110rpx'})`
        });
      }
      this.setBackfillData(this.data.paramsData);
    }
  }
});
